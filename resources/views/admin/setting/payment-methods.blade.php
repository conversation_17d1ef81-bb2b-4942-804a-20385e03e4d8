@extends('admin.layouts.index')

@push('styles')
    <style>
        /* Custom Dropzone Styling */
        .custom-dropzone {
            border: 2px dashed #d9d9d9;
            border-radius: 5px;
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .custom-dropzone:hover {
            border-color: #a0a0a0;
        }

        .custom-dropzone-message {
            text-align: center;
            width: 100%;
            padding: 20px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Hide the message when files are added */
        .custom-dropzone.has-files .custom-dropzone-message {
            display: none;
        }

        /* File preview container */
        .custom-dropzone-previews {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            padding: 15px;
            width: 100%;
            min-height: 100%;
        }

        /* Empty previews container should not take space */
        .custom-dropzone-previews:empty {
            display: none;
        }

        /* Individual file preview */
        .custom-file-preview {
            margin: 10px;
            width: 120px;
            text-align: center;
        }

        .custom-file-preview-image {
            width: 120px;
            height: 120px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 8px;
            position: relative;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px auto;
        }

        .custom-file-preview-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .custom-file-preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #e0e0e0;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #5E6278;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            z-index: 100;
            transition: all 0.2s ease;
        }

        .custom-file-preview-remove:hover {
            background: #ffffff;
            color: #009ef7;
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
        }

        .custom-file-preview-name {
            font-size: 12px;
            color: #5e6278;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .custom-file-preview-size {
            font-size: 11px;
            color: #a1a5b7;
        }

        /* Hidden file input */
        .custom-dropzone-input {
            display: none;
        }
    </style>
@endpush

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">Payment Methods</h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.setting') }}" class="text-hover-primary">Settings</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                Payment Methods
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Payment Methods</h2>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-8">
                            <div class="d-flex flex-column">
                                <h4 class="mb-1 text-dark">Payment Information</h4>
                                <span>The bank account details and QR code image configured on this page will be displayed to users when they proceed with payment. Please ensure all information is accurate and up-to-date.</span>
                            </div>
                        </div>

                        {!! html()->form('POST', route('admin.setting.payment-methods.update'))->class('form')->acceptsFiles()->open() !!}

                        <div class="row mb-8">
                            <div class="col-md-6">
                                <div class="mb-5">
                                    <label class="form-label required">Bank Name</label>
                                    <input type="text" name="bank_name" class="form-control @error('bank_name') is-invalid @enderror"
                                        value="{{ old('bank_name', $paymentMethods['bank_name'] ?? '') }}" required>
                                    @error('bank_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-5">
                                    <label class="form-label required">Bank Account Number</label>
                                    <input type="text" name="bank_account_number" class="form-control @error('bank_account_number') is-invalid @enderror"
                                        value="{{ old('bank_account_number', $paymentMethods['bank_account_number'] ?? '') }}" required>
                                    @error('bank_account_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-8">
                            <div class="col-md-6">
                                <div class="mb-5">
                                    <label class="form-label required">Bank Account Name</label>
                                    <input type="text" name="bank_account_name" class="form-control @error('bank_account_name') is-invalid @enderror"
                                        value="{{ old('bank_account_name', $paymentMethods['bank_account_name'] ?? '') }}" required>
                                    @error('bank_account_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row mb-8">
                            <div class="col-md-12">
                                <div class="mb-5">
                                    <label class="form-label">QR Code Image</label>
                                    <div class="custom-dropzone" id="custom-dropzone">
                                        <div class="custom-dropzone-message">
                                            <i class="ki-outline ki-file-up fs-3x text-gray-500 d-block mx-auto mb-4"></i>
                                            <h3 class="fs-4 fw-bold text-gray-700 mb-3">Drop QR code image here or click to upload</h3>
                                            <span class="fs-6 fw-semibold text-gray-500 d-block">Upload only one image file (max 2MB)</span>
                                        </div>
                                        <div class="custom-dropzone-previews"></div>
                                        <input type="file" class="custom-dropzone-input" id="qr_code_image" name="qr_code_image" accept="image/*">
                                    </div>
                                    @error('qr_code_image')
                                        <div class="text-danger mt-2">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>



                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.setting') }}" class="btn btn-light btn-sm me-3">Cancel</a>
                            <button type="submit" class="btn btn-sm btn-primary">
                                <span class="indicator-label">Save Changes</span>
                            </button>
                        </div>

                        {!! html()->form()->close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Custom file upload handling
        const dropzone = document.getElementById('custom-dropzone');
        const fileInput = document.getElementById('qr_code_image');
        const previewsContainer = document.querySelector('.custom-dropzone-previews');
        const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
        let currentFile = null;

        // Check if there's an existing image
        @if(isset($paymentMethods['qr_code_image_path']))
            // Show the existing image in the preview
            const existingImageUrl = "{{ route('admin.setting.payment-methods.qr-code') }}";
            const existingImageName = "{{ $paymentMethods['qr_code_original_filename'] ?? 'qr-code.png' }}";

            // Create a mock file object for display purposes
            const mockFile = {
                name: existingImageName,
                size: 0, // We don't know the size
                type: "{{ $paymentMethods['qr_code_mime_type'] ?? 'image/png' }}"
            };

            // Display the existing image
            previewFile(mockFile, existingImageUrl);
            dropzone.classList.add('has-files');

            // We don't need to set the file input value here since we're just displaying
            // an existing image and the user hasn't changed anything yet
            currentFile = mockFile;
        @endif

        // Open file dialog when clicking on the dropzone
        dropzone.addEventListener('click', function(e) {
            if (e.target.closest('.custom-file-preview-remove')) {
                return; // Don't open file dialog when clicking remove button
            }
            fileInput.click();
        });

        // Handle drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Highlight dropzone on drag over
        ['dragenter', 'dragover'].forEach(eventName => {
            dropzone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropzone.style.borderColor = '#a0a0a0';
        }

        function unhighlight() {
            dropzone.style.borderColor = '#d9d9d9';
        }

        // Handle dropped files
        dropzone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const newFiles = dt.files;
            if (newFiles.length > 0) {
                handleFile(newFiles[0]);
            }
        }

        // Handle files from input
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                handleFile(this.files[0]);
            }
        });

        function handleFile(file) {
            // Clear existing file
            if (currentFile) {
                removeFile();
            }

            // Check file size
            if (file.size > MAX_FILE_SIZE) {
                alert(`File ${file.name} is too large. Maximum size is 2MB.`);
                return;
            }

            // Check file type
            if (!file.type.match('image.*')) {
                alert(`File ${file.name} is not an image. Please upload an image file.`);
                return;
            }

            currentFile = file;
            previewFile(file);

            // Show/hide message based on file
            dropzone.classList.add('has-files');

            // Update the file input
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
        }

        function previewFile(file, existingUrl = null) {
            // Clear existing previews
            previewsContainer.innerHTML = '';

            const preview = document.createElement('div');
            preview.className = 'custom-file-preview';
            preview.dataset.filename = file.name;

            const imageContainer = document.createElement('div');
            imageContainer.className = 'custom-file-preview-image';

            const removeBtn = document.createElement('div');
            removeBtn.className = 'custom-file-preview-remove';
            removeBtn.innerHTML = '<i class="ki-outline ki-cross fs-7"></i>';
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                removeFile();
            });

            const nameElement = document.createElement('div');
            nameElement.className = 'custom-file-preview-name';
            nameElement.textContent = file.name;

            const sizeElement = document.createElement('div');
            sizeElement.className = 'custom-file-preview-size';
            sizeElement.textContent = file.size ? formatFileSize(file.size) : '';

            // Display the image
            if (existingUrl) {
                // Use the existing URL
                const img = document.createElement('img');
                img.src = existingUrl;
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'cover';
                imageContainer.appendChild(img);
            } else {
                // Create a preview from the file
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'cover';
                    imageContainer.appendChild(img);
                };
                reader.readAsDataURL(file);
            }

            imageContainer.appendChild(removeBtn);
            preview.appendChild(imageContainer);
            preview.appendChild(nameElement);
            preview.appendChild(sizeElement);

            previewsContainer.appendChild(preview);
        }

        function removeFile() {
            // Clear the file input
            fileInput.value = '';
            currentFile = null;

            // Clear preview
            previewsContainer.innerHTML = '';

            // Show message
            dropzone.classList.remove('has-files');
        }

        function formatFileSize(bytes) {
            if (bytes >= 1000000) {
                return (bytes / 1000000).toFixed(2) + ' MB';
            }
            if (bytes >= 1000) {
                return (bytes / 1000).toFixed(2) + ' KB';
            }
            return bytes + ' bytes';
        }
    });
</script>
@endpush
