@extends('admin.layouts.index')

@section('title', 'Leave Approvals')

@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                            Leave Approvals
                        </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                Leave Approvals
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card">
                        <div class="card-header border-0 pt-6">
                            <div class="card-title">
                                <h2>Pending Leave Approvals</h2>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="table-responsive">
                                <table id="dataTable" class="table table-row-dashed dataTable no-footer">
                                    <thead>
                                        <tr class="fw-semibold fs-6 text-muted">
                                            <th>Employee</th>
                                            <th>Company</th>
                                            <th>Type</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Duration</th>
                                            <th>Applied On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#dataTable').DataTable({
            ajax: {
                url: "{{ route('admin.leave-approval.query') }}",
                method: "POST"
            },
            serverSide: true,
            order: [
                [6, 'desc']
            ],
            bFilter: true,
            columns: [{
                    data: "employee_info",
                    orderable: false,
                    searchable: true
                },
                {
                    data: "company_name",
                    orderable: true,
                    searchable: true
                },
                {
                    data: "type",
                    orderable: false
                },
                {
                    data: "start_date",
                    searchable: false
                },
                {
                    data: "end_date",
                    searchable: false
                },
                {
                    data: "duration",
                    searchable: false,
                    render: function(data) {
                        return data + ' day(s)';
                    }
                },
                {
                    data: "created_at",
                    searchable: false
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false
                }
            ]
        });
    </script>
@endpush
