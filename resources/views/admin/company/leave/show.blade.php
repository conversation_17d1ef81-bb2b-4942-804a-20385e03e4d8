@extends('admin.layouts.index')

@section('title', 'Leave Details')

@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            Leave Details
                        </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin.company') }}" class="text-hover-primary">Companies</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.show', $company) }}" class="text-hover-primary">{{ $company->name }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.leave', $company) }}" class="text-hover-primary">Leave Management</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                Leave Details
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                @include('admin.company._menu')

                <div class="d-flex flex-column flex-xl-row">
                    <!-- Left column with leave details -->
                    <div class="flex-column flex-lg-row-auto w-100 w-xl-350px mb-10">
                        <div class="card mb-5 mb-xl-8">
                            <div class="card-header border-0 pt-5">
                                <h3 class="card-title align-items-start flex-column">
                                    <span class="card-label fw-bold text-dark">Leave Information</span>
                                    <span class="text-muted mt-1 fw-semibold fs-7">Details of the leave application</span>
                                </h3>
                                <div class="card-toolbar">
                                    <a href="{{ route('admin.company.leave', $company) }}" class="btn btn-sm btn-secondary">
                                        <i class="ki-outline ki-arrow-left fs-2"></i>Back to List
                                    </a>
                                </div>
                            </div>
                            <div class="card-body pt-9 pb-0">
                                <!-- Leave status indicator -->
                                <div class="d-flex flex-center flex-column mb-5">
                                    <div class="symbol symbol-100px symbol-circle mb-7">
                                        @if($leave->approved_at)
                                            <span class="symbol-label bg-light-success">
                                                <i class="ki-outline ki-check-circle fs-1 text-success"></i>
                                            </span>
                                        @elseif($leave->rejected_at)
                                            <span class="symbol-label bg-light-danger">
                                                <i class="ki-outline ki-cross-circle fs-1 text-danger"></i>
                                            </span>
                                        @else
                                            <span class="symbol-label bg-light-warning">
                                                <i class="ki-outline ki-time fs-1 text-warning"></i>
                                            </span>
                                        @endif
                                    </div>
                                    <div class="fs-3 text-gray-800 fw-bold mb-1">
                                        @if($leave->approved_at)
                                            Approved
                                        @elseif($leave->rejected_at)
                                            Rejected
                                        @else
                                            Pending Approval
                                        @endif
                                    </div>
                                    <div class="fs-5 fw-semibold text-muted mb-6">
                                        @if($leave->type == 'AL')
                                            Annual Leave
                                        @elseif($leave->type == 'MC')
                                            Medical Leave
                                        @elseif($leave->type == 'NPL')
                                            No Pay Leave
                                        @endif
                                    </div>
                                </div>

                                <!-- Leave details -->
                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold">Employee</div>
                                    <div class="text-gray-700">{{ $leave->employee->name }}</div>
                                </div>
                                <div class="separator separator-dashed my-3"></div>

                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold">Duration</div>
                                    <div class="text-gray-700">
                                        {{ $leave->duration }} day(s)
                                        @if($leave->is_half_day)
                                            (Half Day)
                                        @endif
                                    </div>
                                </div>
                                <div class="separator separator-dashed my-3"></div>

                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold">Start Date</div>
                                    <div class="text-gray-700">{{ $leave->start_date->format('d M Y') }}</div>
                                </div>
                                <div class="separator separator-dashed my-3"></div>

                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold">End Date</div>
                                    <div class="text-gray-700">{{ $leave->end_date->format('d M Y') }}</div>
                                </div>
                                <div class="separator separator-dashed my-3"></div>

                                <div class="d-flex flex-stack fs-4 py-3">
                                    <div class="fw-bold">Applied On</div>
                                    <div class="text-gray-700">{{ $leave->created_at->format('d M Y') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right column with additional details -->
                    <div class="flex-lg-row-fluid ms-lg-15">
                        <div class="card mb-5 mb-xl-10">
                            <div class="card-header border-0">
                                <div class="card-title m-0">
                                    <h3 class="fw-bold m-0">Additional Details</h3>
                                </div>
                            </div>
                            <div class="card-body pt-9 pb-0">
                                @if($leave->approved_at)
                                    <div class="notice d-flex bg-light-success rounded border-success border border-dashed p-6 mb-7">
                                        <i class="ki-outline ki-check-circle fs-2tx text-success me-4"></i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-gray-900 fw-bold">Leave Approved</h4>
                                                <div class="fs-6 text-gray-700">
                                                    Approved on {{ $leave->approved_at->format('d M Y, h:i A') }}
                                                    @if($leave->approver)
                                                        by {{ $leave->approver->name }}
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @elseif($leave->rejected_at)
                                    <div class="notice d-flex bg-light-danger rounded border-danger border border-dashed p-6 mb-7">
                                        <i class="ki-outline ki-cross-circle fs-2tx text-danger me-4"></i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-gray-900 fw-bold">Leave Rejected</h4>
                                                <div class="fs-6 text-gray-700">
                                                    Rejected on {{ $leave->rejected_at->format('d M Y, h:i A') }}
                                                    @if($leave->rejecter)
                                                        by {{ $leave->rejecter->name }}
                                                    @endif
                                                </div>
                                                @if($leave->reject_reason)
                                                    <div class="mt-3">
                                                        <h5 class="text-dark">Rejection Reason:</h5>
                                                        <div class="fs-6 text-gray-700">{{ $leave->reject_reason }}</div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 mb-7">
                                        <i class="ki-outline ki-time fs-2tx text-warning me-4"></i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-gray-900 fw-bold">Pending Approval</h4>
                                                <div class="fs-6 text-gray-700">This leave application is waiting for your approval</div>
                                            </div>
                                        </div>
                                    </div>

                                @endif

                                @if($leave->remark)
                                    <div class="mb-8">
                                        <h4 class="text-gray-800 mb-3">Employee's Remarks</h4>
                                        <div class="rounded border border-dashed border-gray-300 p-6">
                                            <div class="fs-6 text-gray-700">{{ $leave->remark }}</div>
                                        </div>
                                    </div>
                                @endif

                                @if($leave->attachments->count() > 0)
                                    <div class="mb-8">
                                        <h4 class="text-gray-800 mb-3">Attachments</h4>
                                        <div class="rounded border border-dashed border-gray-300 p-6">
                                            <div class="table-responsive">
                                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                                    <thead>
                                                        <tr class="fw-bold text-muted">
                                                            <th class="min-w-150px">File Name</th>
                                                            <th class="min-w-100px">Size</th>
                                                            <th class="min-w-100px text-end">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($leave->attachments as $attachment)
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <i class="fa {{ $attachment->file_icon }} fa-2x text-primary me-4"></i>
                                                                        <div class="d-flex flex-column">
                                                                            <a href="{{ route('admin.company.leave.attachment.preview', [$company, $leave, $attachment]) }}" target="_blank" class="text-gray-800 text-hover-primary mb-1 fw-bold">{{ $attachment->original_filename }}</a>
                                                                            <span class="text-muted">{{ $attachment->mime_type }}</span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>{{ $attachment->formatted_file_size }}</td>
                                                                <td class="text-end">
                                                                    <a href="{{ route('admin.company.leave.attachment.download', [$company, $leave, $attachment]) }}" class="btn btn-sm btn-icon btn-light-primary">
                                                                        <i class="ki-outline ki-cloud-download fs-2"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                @if(!$leave->approved_at && !$leave->rejected_at)
                                    <div class="d-flex justify-content-end mt-8 mb-5">
                                        <a href="{{ route('admin.company.leave.reject', [$company, $leave]) }}" class="btn btn-sm btn-danger me-3">
                                            Reject Leave
                                        </a>
                                        <a href="{{ route('admin.company.leave.approve', [$company, $leave]) }}" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this leave?');">
                                            Approve Leave
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
