@extends('admin.layouts.index')

@section('title', 'Reject Leave')

@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            Reject Leave
                        </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin.company') }}" class="text-hover-primary">Companies</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.show', $company) }}" class="text-hover-primary">{{ $company->name }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.leave', $company) }}" class="text-hover-primary">Leave Management</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.leave.show', [$company, $leave]) }}" class="text-hover-primary">Leave Details</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                Reject Leave
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                @include('admin.company._menu')

                <div class="row">
                    <div class="col-xl-8 mx-auto">
                        <div class="card card-flush mb-6">
                            <div class="card-header">
                                <div class="card-title">
                                    <h2 class="fw-bold text-dark">Reject Leave Application</h2>
                                </div>
                                <div class="card-toolbar">
                                    <a href="{{ route('admin.company.leave.show', [$company, $leave]) }}" class="btn btn-sm btn-secondary">
                                        <i class="ki-outline ki-arrow-left fs-2"></i>Back to Details
                                    </a>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <!-- Leave application summary -->
                                <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed mb-9 p-6">
                                    <i class="ki-outline ki-information-5 fs-2tx text-warning me-4"></i>
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <div>
                                            <h4 class="text-gray-900 fw-bold mb-0">Leave Application Summary</h4>
                                            <p class="fs-6 text-gray-700 mt-2 mb-0">You are about to reject the following leave application. Please provide a reason for the rejection.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Leave details cards -->
                                <div class="row g-5 g-xl-9 mb-8">
                                    <!-- Employee card -->
                                    <div class="col-md-6 col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body p-5">
                                                <div class="d-flex align-items-center">
                                                    <div class="symbol symbol-50px me-5">
                                                        <span class="symbol-label bg-light-primary">
                                                            <i class="ki-outline ki-user fs-2x text-primary"></i>
                                                        </span>
                                                    </div>
                                                    <div class="d-flex flex-column">
                                                        <h4 class="mb-1 text-dark">{{ $leave->employee->name }}</h4>
                                                        <span class="text-muted fw-semibold">Employee</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Leave type card -->
                                    <div class="col-md-6 col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body p-5">
                                                <div class="d-flex align-items-center">
                                                    <div class="symbol symbol-50px me-5">
                                                        <span class="symbol-label bg-light-success">
                                                            <i class="ki-outline ki-calendar fs-2x text-success"></i>
                                                        </span>
                                                    </div>
                                                    <div class="d-flex flex-column">
                                                        <h4 class="mb-1 text-dark">
                                                            @if($leave->type == 'AL')
                                                                Annual Leave
                                                            @elseif($leave->type == 'MC')
                                                                Medical Leave
                                                            @elseif($leave->type == 'NPL')
                                                                No Pay Leave
                                                            @endif
                                                        </h4>
                                                        <span class="text-muted fw-semibold">Leave Type</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Duration card -->
                                    <div class="col-md-6 col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body p-5">
                                                <div class="d-flex align-items-center">
                                                    <div class="symbol symbol-50px me-5">
                                                        <span class="symbol-label bg-light-danger">
                                                            <i class="ki-outline ki-timer fs-2x text-danger"></i>
                                                        </span>
                                                    </div>
                                                    <div class="d-flex flex-column">
                                                        <h4 class="mb-1 text-dark">
                                                            {{ $leave->duration }} day(s)
                                                            @if($leave->is_half_day)
                                                                (Half Day)
                                                            @endif
                                                        </h4>
                                                        <span class="text-muted fw-semibold">Duration</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Period card -->
                                    <div class="col-md-6 col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body p-5">
                                                <div class="d-flex align-items-center">
                                                    <div class="symbol symbol-50px me-5">
                                                        <span class="symbol-label bg-light-info">
                                                            <i class="ki-outline ki-calendar-8 fs-2x text-info"></i>
                                                        </span>
                                                    </div>
                                                    <div class="d-flex flex-column">
                                                        <h4 class="mb-1 text-dark">{{ $leave->start_date->format('d M Y') }} - {{ $leave->end_date->format('d M Y') }}</h4>
                                                        <span class="text-muted fw-semibold">Period</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rejection form -->
                                {!! html()->form('POST', route('admin.company.leave.reject', [$company, $leave]))->class('form')->open() !!}
                                    <div class="mb-8">
                                        <h3 class="fw-bold text-dark mb-5">Rejection Reason</h3>
                                        <div class="mb-5">
                                            <label class="form-label required fs-6 fw-semibold">Please provide a detailed reason for rejecting this leave application</label>
                                            <textarea name="reject_reason" class="form-control form-control-solid @error('reject_reason') is-invalid @enderror" rows="6" placeholder="Enter rejection reason here..." required></textarea>
                                            @error('reject_reason')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text text-muted">This reason will be visible to the employee.</div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end mb-5">
                                        <a href="{{ route('admin.company.leave.show', [$company, $leave]) }}" class="btn btn-sm btn-light me-3">Cancel</a>
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <span class="indicator-label">Reject Leave</span>
                                        </button>
                                    </div>
                                {!! html()->form()->close() !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
