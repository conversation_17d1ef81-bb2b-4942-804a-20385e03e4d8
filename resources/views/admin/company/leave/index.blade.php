@extends('admin.layouts.index')

@section('title', 'Leave Management')

@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            Leave Management
                        </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin.company') }}" class="text-hover-primary">Companies</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.company.show', $company) }}" class="text-hover-primary">{{ $company->name }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                Leave Management
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                @include('admin.company._menu')

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Leave Applications</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="dataTable" class="table table-row-dashed dataTable no-footer">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th>Employee</th>
                                        <th>Type</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#dataTable').DataTable({
            ajax: {
                url: "{{ route('admin.company.leave.query', $company) }}",
                method: "POST"
            },
            serverSide: true,
            order: [
                [6, 'desc']
            ],
            bFilter: true,
            columns: [{
                    data: "employee_info",
                    orderable: false,
                    searchable: true
                },
                {
                    data: "type",
                    orderable: false
                },
                {
                    data: "start_date",
                    searchable: false
                },
                {
                    data: "end_date",
                    searchable: false
                },
                {
                    data: "duration",
                    searchable: false,
                    render: function(data) {
                        return data + ' day(s)';
                    }
                },
                {
                    data: "status",
                    orderable: false,
                    searchable: false
                },
                {
                    data: "created_at",
                    searchable: false
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false
                }
            ]
        });
    </script>
@endpush
