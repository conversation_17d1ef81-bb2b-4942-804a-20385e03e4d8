<div class="card-header border-0 cursor-pointer" role="button">
    <div class="card-title m-0">
        <h3 class="fw-bold m-0">Basic Information</h3>
    </div>
</div>

<div class="card-body border-top p-9">
    <div class="row">

        @foreach ($languages as $language)
            <div class="col-lg-6">
                {!! html()->label('Name')->class($loop->iteration === 1 ? 'form-label required' : 'form-label') !!}
                <div class="input-group mb-10">
                    <span class="input-group-text">{{ strtoupper($language->name) }}</span>
                    {!! html()->text('name[' . $language->code . ']')->class('form-control' . $errors->first('name.' . $language->code, ' is-invalid'))->value(isset($group) ? getTranslation($group, 'name', $language->code) : old('name.' . $language->code)) !!}
                    @error('name.' . $language->code)
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
            </div>
        @endforeach

        <div class="col-lg-6">
            {!! html()->label('Slug')->class('form-label') !!}
            <i class="fas fa-question-circle" data-bs-toggle="tooltip" title="For pretty url purpose. Leave blank or consult your developer if you don't know what this is." data-bs-html="true"></i>
            <div class="input-group has-validation mb-10">
                {!! html()->text('slug')->class('form-control ' . $errors->first('slug', ' is-invalid')) !!}
                <div class="invalid-feedback">
                    @error('slug')
                        {{ $message }}
                    @enderror
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card-header border-0 cursor-pointer" role="button">
    <div class="card-title m-0">
        <h3 class="fw-bold m-0">Status</h3>
    </div>
</div>

<div class="card-body border-top p-9">
    <div class="row">
        <div class="col-lg-4 align-self-center">
            <div class="d-flex flex-stack mb-10">
                <div class="me-5">
                    <label class="fs-6 fw-semibold">Active & Inactive</label>
                    <div class="fs-7 fw-semibold text-muted">To be appear to the public</div>
                </div>
                <label class="form-check form-switch form-check-custom form-check-solid form-check-success">
                    {!! html()->hidden('is_active', 0) !!}
                    {!! html()->checkbox('is_active', null, 1)->class('form-check-input') !!}
                </label>
            </div>
        </div>
    </div>
</div>
