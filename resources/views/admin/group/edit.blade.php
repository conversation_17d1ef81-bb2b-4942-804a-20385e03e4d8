@extends('admin.layouts.index')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">Edit Group</h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin.group') }}" class="text-hover-primary">Groups</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">Edit</li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        {!! html()->form('DELETE', route('admin.group.delete', $group))->class('form')->open() !!}
                        <button class="btn btn-sm btn-flex btn-danger" onclick="return confirm('Are you sure you want to delete this?')">
                            <i class="ki-outline ki-trash fs-4"></i>Delete
                        </button>
                        {!! html()->form()->close() !!}
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                {{ html()->modelForm($group, 'POST', route('admin.group.update', $group))->class('form')->acceptsFiles()->open() }}
                <div class="card">
                    @include('admin.group._form')
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        {!! html()->submit('Save')->class('btn btn-primary btn-sm') !!}
                    </div>
                </div>
                {{ html()->closeModelForm() }}
            </div>
        </div>
    </div>
@endsection
