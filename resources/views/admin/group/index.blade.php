@extends('admin.layouts.index')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">Groups</h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">Groups</li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        <a href="{{ route('admin.group.create') }}" class="btn btn-sm btn-flex btn-primary">
                            <i class="ki-outline ki-plus fs-4"></i>Create
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="dataTable" class="table table-row-dashed dataTable no-footer">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Created at</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#dataTable').DataTable({
            ajax: {
                url: "{{ route('admin.group.query') }}",
                method: "POST"
            },
            serverSide: true,
            order: [
                [2, 'desc']
            ],
            bFilter: true,
            pageLength: 10,
            columns: [{
                    data: "translations.name",
                    orderable: false
                },
                {
                    data: "is_active",
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        return data == 1 ? '<span class="badge badge-light-success">Active</span>' : '<span class="badge badge-light-danger">Inactive</span>';
                    }
                },
                {
                    data: "created_at",
                    searchable: false
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false
                }
            ]
        });


    </script>
@endpush
