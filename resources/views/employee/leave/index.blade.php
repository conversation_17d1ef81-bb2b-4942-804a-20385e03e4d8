@extends('employee.layouts.index')

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-fluid">
            <!-- Leave Balance Cards -->
            <div class="row g-6 g-xl-9 mb-8">
                <div class="col-xl-6">
                    <div class="card card-xl-stretch mb-xl-8 bg-light-info">
                        <div class="card-body p-10">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-5">
                                    <span class="symbol symbol-50px me-5">
                                        <span class="symbol-label bg-info">
                                            <i class="ki-outline ki-calendar-tick fs-1 text-white"></i>
                                        </span>
                                    </span>
                                    <div class="d-flex flex-column">
                                        <span class="text-dark fw-bold fs-1 mb-0">{{ Auth::user()->yearly_annual_leave_entitlement - Auth::user()->taken_annual_leave }}
                                            / {{ Auth::user()->yearly_annual_leave_entitlement }}</span>
                                        <span class="text-muted fw-semibold fs-6">Annual Leave Balance</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="progress h-10px w-100 bg-info bg-opacity-25">
                                        @php
                                            $annualPercentage = Auth::user()->yearly_annual_leave_entitlement > 0 ? ((Auth::user()->yearly_annual_leave_entitlement - Auth::user()->taken_annual_leave) / Auth::user()->yearly_annual_leave_entitlement) * 100 : 0;
                                        @endphp
                                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ $annualPercentage }}%" aria-valuenow="{{ $annualPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="card card-xl-stretch mb-xl-8 bg-light-warning">
                        <div class="card-body p-10">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-5">
                                    <span class="symbol symbol-50px me-5">
                                        <span class="symbol-label bg-warning">
                                            <i class="ki-outline ki-calendar-tick fs-1 text-white"></i>
                                        </span>
                                    </span>
                                    <div class="d-flex flex-column">
                                        <span class="text-dark fw-bold fs-1 mb-0">{{ Auth::user()->yearly_medical_leave_entitlement - Auth::user()->taken_medical_leave }}
                                            / {{ Auth::user()->yearly_medical_leave_entitlement }}</span>
                                        <span class="text-muted fw-semibold fs-6">Medical Leave Balance</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="progress h-10px w-100 bg-warning bg-opacity-25">
                                        @php
                                            $medicalPercentage = Auth::user()->yearly_medical_leave_entitlement > 0 ? ((Auth::user()->yearly_medical_leave_entitlement - Auth::user()->taken_medical_leave) / Auth::user()->yearly_medical_leave_entitlement) * 100 : 0;
                                        @endphp
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ $medicalPercentage }}%" aria-valuenow="{{ $medicalPercentage }}" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leave Applications Table -->
            <div class="card">
                <div class="card-header border-0 pt-6">
                    <div class="card-title">
                        <h2>Leave Applications</h2>
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('employee.leave.create', $company) }}" class="btn btn-sm btn-primary">
                            <i class="ki-outline ki-plus fs-2"></i>Apply Leave
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="leave-table" class="table table-row-dashed dataTable no-footer">
                            <thead>
                                <tr class="fw-semibold fs-6 text-muted">
                                    <th>Type</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                    <th class="text-end">Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#leave-table').DataTable({
            ajax: {
                url: "{{ route('employee.leave.query', $company) }}",
                method: "POST"
            },
            serverSide: true,
            order: [
                [5, 'desc'] // Sort by created_at column (index 5) in descending order
            ],
            bFilter: true,
            columns: [{
                    data: "type",
                    orderable: false
                },
                {
                    data: "start_date",
                    searchable: false
                },
                {
                    data: "end_date",
                    searchable: false
                },
                {
                    data: "duration",
                    searchable: false,
                    render: function(data) {
                        return data + ' day(s)';
                    }
                },
                {
                    data: "status",
                    orderable: false,
                    searchable: false
                },
                {
                    data: "created_at",
                    searchable: false
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false
                }
            ]
        });
    </script>
@endpush
