@extends('employee.layouts.index')

@push('styles')
    <style>
        /* Custom Dropzone Styling */
        .custom-dropzone {
            border: 2px dashed #d9d9d9;
            border-radius: 5px;
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .custom-dropzone:hover {
            border-color: #a0a0a0;
        }

        .custom-dropzone-message {
            text-align: center;
            width: 100%;
            padding: 20px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Hide the message when files are added */
        .custom-dropzone.has-files .custom-dropzone-message {
            display: none;
        }

        /* File preview container */
        .custom-dropzone-previews {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            padding: 15px;
            width: 100%;
            min-height: 100%;
        }

        /* Empty previews container should not take space */
        .custom-dropzone-previews:empty {
            display: none;
        }

        /* Individual file preview */
        .custom-file-preview {
            margin: 10px;
            width: 120px;
            text-align: center;
        }

        .custom-file-preview-image {
            width: 120px;
            height: 120px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 8px;
            position: relative;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px auto;
        }

        .custom-file-preview-image img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .custom-file-preview-icon {
            font-size: 40px;
            color: #a0a0a0;
        }

        .custom-file-preview-name {
            font-size: 12px;
            color: #5E6278;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 5px;
        }

        .custom-file-preview-size {
            font-size: 11px;
            color: #7E8299;
        }

        .custom-file-preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #e0e0e0;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #5E6278;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            z-index: 100;
            transition: all 0.2s ease;
        }

        .custom-file-preview-remove:hover {
            background: #ffffff;
            color: #009ef7;
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
        }

        /* Hidden file input */
        .custom-dropzone-input {
            display: none;
        }
    </style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-fluid">
            <!-- Leave Balance Cards -->
            <div class="row g-6 g-xl-9 mb-8">
                <div class="col-xl-6">
                    <div class="card card-xl-stretch mb-xl-8 bg-light-info">
                        <div class="card-body p-10">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-5">
                                    <span class="symbol symbol-50px me-5">
                                        <span class="symbol-label bg-info">
                                            <i class="ki-outline ki-calendar-tick fs-1 text-white"></i>
                                        </span>
                                    </span>
                                    <div class="d-flex flex-column">
                                        <span class="text-dark fw-bold fs-1 mb-0">{{ Auth::user()->yearly_annual_leave_entitlement - Auth::user()->taken_annual_leave }} / {{ Auth::user()->yearly_annual_leave_entitlement }}</span>
                                        <span class="text-muted fw-semibold fs-6">Annual Leave Balance</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="progress h-10px w-100 bg-info bg-opacity-25">
                                        @php
                                            $annualPercentage = Auth::user()->yearly_annual_leave_entitlement > 0 ? ((Auth::user()->yearly_annual_leave_entitlement - Auth::user()->taken_annual_leave) / Auth::user()->yearly_annual_leave_entitlement) * 100 : 0;
                                        @endphp
                                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ $annualPercentage }}%" aria-valuenow="{{ $annualPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="card card-xl-stretch mb-xl-8 bg-light-warning">
                        <div class="card-body p-10">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-5">
                                    <span class="symbol symbol-50px me-5">
                                        <span class="symbol-label bg-warning">
                                            <i class="ki-outline ki-calendar-tick fs-1 text-white"></i>
                                        </span>
                                    </span>
                                    <div class="d-flex flex-column">
                                        <span class="text-dark fw-bold fs-1 mb-0">{{ Auth::user()->yearly_medical_leave_entitlement - Auth::user()->taken_medical_leave }} / {{ Auth::user()->yearly_medical_leave_entitlement }}</span>
                                        <span class="text-muted fw-semibold fs-6">Medical Leave Balance</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="progress h-10px w-100 bg-warning bg-opacity-25">
                                        @php
                                            $medicalPercentage = Auth::user()->yearly_medical_leave_entitlement > 0 ? ((Auth::user()->yearly_medical_leave_entitlement - Auth::user()->taken_medical_leave) / Auth::user()->yearly_medical_leave_entitlement) * 100 : 0;
                                        @endphp
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ $medicalPercentage }}%" aria-valuenow="{{ $medicalPercentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Apply Leave Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h2>Apply Leave</h2>
                    </div>
                </div>
                <div class="card-body">
                    {!! html()->form('POST', route('employee.leave.store', $company))->class('form')->acceptsFiles()->open() !!}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-5">
                                {!! html()->label('Leave Type')->class('form-label required') !!}
                                <div class="input-group has-validation mb-2">
                                    {!! html()->select('type', [
                                            'AL' => 'AL',
                                            'MC' => 'MC',
                                            'NPL' => 'NPL',
                                        ])->class('form-control form-select ' . $errors->first('type', ' is-invalid'))->required() !!}
                                    <div class="invalid-feedback">
                                        @error('type')
                                            {{ $message }}
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-5">
                                {!! html()->label('Start Date')->class('form-label required') !!}
                                <div class="input-group has-validation mb-2">
                                    {!! html()->text('start_date')->class('form-control datepicker ' . $errors->first('start_date', ' is-invalid'))->required() !!}
                                    <div class="invalid-feedback">
                                        @error('start_date')
                                            {{ $message }}
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-5">
                                {!! html()->label('End Date')->class('form-label required') !!}
                                <div class="input-group has-validation mb-2">
                                    {!! html()->text('end_date')->class('form-control datepicker ' . $errors->first('end_date', ' is-invalid'))->required() !!}
                                    <div class="invalid-feedback">
                                        @error('end_date')
                                            {{ $message }}
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-5">
                                <div class="form-check form-switch form-check-custom form-check-solid">
                                    {!! html()->checkbox('is_half_day', false, '1')->class('form-check-input') !!}
                                    {!! html()->label('Half Day Leave')->class('form-check-label fw-semibold text-gray-700')->for('is_half_day') !!}
                                </div>
                                <div class="text-muted fs-7 mt-1">Check this if you're applying for a half-day leave</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-5">
                                {!! html()->label('Remark')->class('form-label') !!}
                                <div class="input-group has-validation mb-2">
                                    {!! html()->textarea('remark')->class('form-control ' . $errors->first('remark', ' is-invalid'))->rows(3) !!}
                                    <div class="invalid-feedback">
                                        @error('remark')
                                            {{ $message }}
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-5">
                                {!! html()->label('Attachments')->class('form-label') !!}
                                <div class="custom-dropzone" id="custom-dropzone">
                                    <div class="custom-dropzone-message">
                                        <i class="ki-outline ki-file-up fs-3x text-gray-500 d-block mx-auto mb-4"></i>
                                        <h3 class="fs-4 fw-bold text-gray-700 mb-3">Drop files here or click to upload</h3>
                                        <span class="fs-6 fw-semibold text-gray-500 d-block">Upload up to 10 files (images, PDFs, documents)</span>
                                    </div>
                                    <div class="custom-dropzone-previews"></div>
                                    <input type="file" multiple class="custom-dropzone-input" id="file-upload" name="attachments[]" accept=".jpeg,.jpg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt">
                                </div>
                                <div id="uploaded-files"></div>
                            </div>
                        </div>
                    </div>

                    @if ($errors->has('error'))
                        <div class="alert alert-danger">
                            {{ $errors->first('error') }}
                        </div>
                    @endif

                    <div class="d-flex justify-content-end">
                        <a href="{{ route('employee.leave', $company) }}" class="btn btn-sm btn-light me-3">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-sm btn-primary">
                            Submit
                        </button>
                    </div>
                    {!! html()->form()->close() !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize datepickers
            $('.datepicker').flatpickr({
                dateFormat: "Y-m-d",
                minDate: "today"
            });

            // Handle half-day checkbox
            $('#is_half_day').on('change', function() {
                if ($(this).is(':checked')) {
                    // If half-day is checked, set end_date to same as start_date
                    $('#end_date').val($('#start_date').val());
                }
            });

            // When start_date changes, update end_date if half-day is checked
            $('#start_date').on('change', function() {
                if ($('#is_half_day').is(':checked')) {
                    $('#end_date').val($(this).val());
                }
            });

            // Custom file upload handling
            const dropzone = document.getElementById('custom-dropzone');
            const fileInput = document.getElementById('file-upload');
            const previewsContainer = document.querySelector('.custom-dropzone-previews');
            const MAX_FILES = 10;
            const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
            let files = [];

            // Open file dialog when clicking on the dropzone
            dropzone.addEventListener('click', function(e) {
                if (e.target.closest('.custom-file-preview-remove')) {
                    return; // Don't open file dialog when clicking remove button
                }
                fileInput.click();
            });

            // Handle drag and drop events
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropzone.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // Highlight dropzone on drag over
            ['dragenter', 'dragover'].forEach(eventName => {
                dropzone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropzone.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropzone.style.borderColor = '#a0a0a0';
            }

            function unhighlight() {
                dropzone.style.borderColor = '#d9d9d9';
            }

            // Handle dropped files
            dropzone.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const newFiles = dt.files;
                handleFiles(newFiles);
            }

            // Handle files from input
            fileInput.addEventListener('change', function() {
                handleFiles(this.files);
            });

            function handleFiles(newFiles) {
                if (files.length + newFiles.length > MAX_FILES) {
                    alert(`You can only upload up to ${MAX_FILES} files.`);
                    return;
                }

                // Add files to our array
                for (let i = 0; i < newFiles.length; i++) {
                    const file = newFiles[i];

                    // Check file size
                    if (file.size > MAX_FILE_SIZE) {
                        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
                        continue;
                    }

                    // Check file type
                    const acceptedTypes = ['.jpeg', '.jpg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'];
                    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
                    if (!acceptedTypes.includes(fileExt)) {
                        alert(`File ${file.name} has an invalid type. Accepted types: ${acceptedTypes.join(', ')}`);
                        continue;
                    }

                    files.push(file);
                    previewFile(file);
                }

                // Show/hide message based on files
                if (files.length > 0) {
                    dropzone.classList.add('has-files');
                } else {
                    dropzone.classList.remove('has-files');
                }

                // Reset the input
                fileInput.value = '';
            }

            function previewFile(file) {
                const reader = new FileReader();
                const preview = document.createElement('div');
                preview.className = 'custom-file-preview';
                preview.dataset.filename = file.name;

                const imageContainer = document.createElement('div');
                imageContainer.className = 'custom-file-preview-image';

                const removeBtn = document.createElement('div');
                removeBtn.className = 'custom-file-preview-remove';
                removeBtn.innerHTML = '<i class="ki-outline ki-cross fs-1" style="line-height: 1;"></i>';
                removeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    removeFile(file.name);
                });

                const nameElement = document.createElement('div');
                nameElement.className = 'custom-file-preview-name';
                nameElement.textContent = file.name;

                const sizeElement = document.createElement('div');
                sizeElement.className = 'custom-file-preview-size';
                sizeElement.textContent = formatFileSize(file.size);

                // Check if it's an image
                if (file.type.match('image.*')) {
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        imageContainer.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                } else {
                    // Show icon based on file type
                    const icon = document.createElement('i');
                    icon.className = 'custom-file-preview-icon fa ' + getFileIcon(file.type);
                    imageContainer.appendChild(icon);
                }

                imageContainer.appendChild(removeBtn);
                preview.appendChild(imageContainer);
                preview.appendChild(nameElement);
                preview.appendChild(sizeElement);

                previewsContainer.appendChild(preview);
            }

            function removeFile(filename) {
                // Remove from files array
                files = files.filter(file => file.name !== filename);

                // Remove preview
                const preview = document.querySelector(`.custom-file-preview[data-filename="${filename}"]`);
                if (preview) {
                    preview.remove();
                }

                // Show/hide message based on files
                if (files.length === 0) {
                    dropzone.classList.remove('has-files');
                }
            }

            function formatFileSize(bytes) {
                if (bytes >= 1000000) {
                    return (bytes / 1000000).toFixed(2) + ' MB';
                }
                if (bytes >= 1000) {
                    return (bytes / 1000).toFixed(2) + ' KB';
                }
                return bytes + ' bytes';
            }

            function getFileIcon(mimeType) {
                if (mimeType.match('image.*')) {
                    return 'fa-file-image';
                } else if (mimeType.match('application/pdf')) {
                    return 'fa-file-pdf';
                } else if (mimeType.match('application/msword') || mimeType.match('application/vnd.openxmlformats-officedocument.wordprocessingml')) {
                    return 'fa-file-word';
                } else if (mimeType.match('application/vnd.ms-excel') || mimeType.match('application/vnd.openxmlformats-officedocument.spreadsheetml')) {
                    return 'fa-file-excel';
                } else if (mimeType.match('text/')) {
                    return 'fa-file-alt';
                } else {
                    return 'fa-file';
                }
            }

            // Handle form submission
            document.querySelector("form").addEventListener("submit", function(e) {
                if (files.length > 0) {
                    e.preventDefault();

                    // Create a FormData object
                    const formData = new FormData(this);

                    // Remove existing file inputs
                    const existingInputs = formData.getAll('attachments[]');
                    for (let i = 0; i < existingInputs.length; i++) {
                        formData.delete('attachments[]');
                    }

                    // Add our files
                    files.forEach(file => {
                        formData.append('attachments[]', file);
                    });

                    // Submit the form
                    for (let [key, value] of formData.entries()) {
                        if (key !== 'attachments[]') {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = key;
                            input.value = value;
                            this.appendChild(input);
                        }
                    }

                    // Create a new DataTransfer object
                    const dataTransfer = new DataTransfer();

                    // Add files to the DataTransfer object
                    files.forEach(file => {
                        dataTransfer.items.add(file);
                    });

                    // Set the files property of the file input
                    fileInput.files = dataTransfer.files;

                    // Submit the form
                    this.submit();
                }
            });
        });
    </script>
@endpush
