<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('web')
                ->as('admin')
                ->prefix('admin')
                ->group(__DIR__.'/../routes/admin.php');

            Route::middleware('web')
                ->as('employee')
                ->prefix('company')
                ->group(__DIR__.'/../routes/employee.php');
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(function () {
            if (Route::is('admin*')) {
                return route('admin.login');
            }

            if (Route::is('employee*')) {
                return route('employee.login');
            }

            return route('login');
        });

        $middleware->redirectUsersTo(function () {
            if (Route::is('admin*')) {
                return route('admin');
            }

            if (Route::is('employee*')) {
                return route('employee.switch');
            }

            return route('me.booking');
        });

        $middleware->web(append: [
            \App\Http\Middleware\SetAppLocale::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
