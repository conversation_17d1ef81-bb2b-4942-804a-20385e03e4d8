<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('logo')->nullable();
            $table->string('registration_no')->unique();
            $table->string('legal_name');
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('description')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_no')->nullable();
            $table->string('bank_swift_code')->nullable();
            $table->string('main_pic_name')->nullable();
            $table->string('main_pic_phone_e164')->nullable();
            $table->string('main_pic_email')->nullable();
            $table->string('main_pic_address1')->nullable();
            $table->string('main_pic_address2')->nullable();
            $table->string('main_pic_country')->nullable();
            $table->string('main_pic_state')->nullable();
            $table->string('main_pic_city')->nullable();
            $table->string('main_pic_postcode')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
