<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('cover')->comment('550 x 550');
            $table->string('slug')->unique();
            $table->double('price', 12, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->foreignUlid('group_id')->constrained();
            $table->foreignUlid('company_id')->constrained();
            $table->foreignUlid('category_id')->constrained();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
