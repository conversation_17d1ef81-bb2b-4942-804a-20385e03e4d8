<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('no')->unique();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone_e164')->nullable();
            $table->float('sub_total', 12, 2)->default(0);
            $table->float('discount', 12, 2)->default(0);
            $table->float('total', 12, 2)->default(0);
            $table->string('tax_name')->nullable();
            $table->float('tax', 12, 2)->default(0);
            $table->float('grand_total', 12, 2)->default(0);
            $table->dateTime('failed_at')->nullable();
            $table->dateTime('paid_at')->nullable();
            $table->dateTime('accepted_at')->nullable();
            $table->dateTime('completed_at')->nullable();
            $table->dateTime('cancelled_at')->nullable();
            $table->longText('google_meet_link')->nullable();
            $table->longText('internal_note')->nullable();
            $table->date('date')->nullable()->comment('Slot date');
            $table->string('time')->nullable()->comment('Time Slot Time');
            $table->longText('user_meta')->nullable();
            $table->longText('time_slot_meta')->nullable();
            $table->longText('service_meta')->nullable();
            $table->string('calendar_event_id')->nullable();
            $table->foreignUlid('service_id')->nullable()->constrained('services');
            $table->foreignUlid('user_id')->nullable()->constrained('users');
            $table->foreignUlid('time_slot_id')->nullable()->constrained('time_slots');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
