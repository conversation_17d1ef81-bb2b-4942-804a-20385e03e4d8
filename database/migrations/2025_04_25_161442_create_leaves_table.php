<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leaves', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->enum('type', ['AL', 'MC', 'NPL']);
            $table->date('start_date');
            $table->date('end_date');
            $table->float('duration', 8, 1)->comment('days');
            $table->boolean('is_half_day')->default(false);
            $table->text('remark')->nullable();
            $table->text('reject_reason')->nullable();
            $table->foreignUlid('employee_id')->constrained();
            $table->nullableUlidMorphs('approver');
            $table->timestamp('approved_at')->nullable();
            $table->nullableUlidMorphs('rejecter');
            $table->timestamp('rejected_at')->nullable();
            $table->foreignUlid('company_id')->constrained();
            $table->ulidMorphs('creator');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leaves');
    }
};
