<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('avatar')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone_e164')->nullable();
            $table->string('gender')->nullable();
            $table->string('employment_status')->nullable();
            $table->string('identity_type')->nullable();
            $table->string('identity_no')->nullable();
            $table->boolean('is_owner')->default(false);
            $table->float('yearly_annual_leave_entitlement', 12, 2)->default(0)->comment('days');
            $table->float('yearly_medical_leave_entitlement', 12, 2)->default(0)->comment('days');
            $table->float('entitled_annual_leave', 12, 2)->default(0)->comment('days');
            $table->float('taken_annual_leave', 12, 2)->default(0)->comment('days');
            $table->float('entitled_medical_leave', 12, 2)->default(0)->comment('days');
            $table->float('taken_medical_leave', 12, 2)->default(0)->comment('days');
            $table->string('password')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignUlid('company_id')->nullable()->constrained();
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
