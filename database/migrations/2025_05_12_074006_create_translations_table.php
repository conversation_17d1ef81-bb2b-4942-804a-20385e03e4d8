<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('translations', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('language_code'); // e.g., 'en', 'fr', 'es'
            $table->ulidMorphs('translatable');
            $table->string('title')->nullable(); // Translated name
            $table->string('button_text')->nullable(); // Translated button text
            $table->string('name')->nullable(); // Translated name
            $table->longText('description')->nullable(); // Translated description
            $table->longText('content')->nullable(); // Translated content (optional)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('translations');
    }
};
