<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('receipt_path')->nullable()->after('payload');
            $table->string('receipt_original_filename')->nullable()->after('receipt_path');
            $table->string('receipt_mime_type')->nullable()->after('receipt_original_filename');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('receipt_path');
            $table->dropColumn('receipt_original_filename');
            $table->dropColumn('receipt_mime_type');
        });
    }
};
