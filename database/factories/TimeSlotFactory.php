<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Banner>
 */
class TimeSlotFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'date' => fake()->dateTimeBetween('-1 week', '+1 week'),
            'time' => fake()->time('H:i'),
            'slotable_id' => fake()->numberBetween(1, 10),
            'slotable_type' => fake()->randomElement(['App\Models\Blog', 'App\Models\Service']),
        ];
    }
}
