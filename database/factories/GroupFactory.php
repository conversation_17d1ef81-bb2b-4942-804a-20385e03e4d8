<?php

namespace Database\Factories;

use App\Models\Group;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class GroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'slug' => fake()->slug,
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Group $group) {
            // Create translations for each language
            $languages = ['en', 'zh']; // Add more languages as needed

            foreach ($languages as $lang) {
                $group->translations()->create([
                    'language_code' => $lang,
                    'name' => fake()->sentence,
                ]);
            }
        });
    }
}
