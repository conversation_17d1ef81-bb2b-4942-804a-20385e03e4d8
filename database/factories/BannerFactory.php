<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Banner>
 */
class BannerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cover' => 'https://placehold.co/1920x1000',
            'cover_landscape' => 'https://placehold.co/1050x550',
            'slug' => fake()->slug,
            'external_url' => fake()->url,
            'sequence' => fake()->numberBetween($min = 1, $max = 100),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($banner) {
            // Create translations for each language
            $languages = ['en', 'zh']; // Add more languages as needed

            foreach ($languages as $lang) {
                $banner->translations()->create([
                    'language_code' => $lang,
                    'title' => fake()->sentence,
                    'description' => fake()->sentence,
                    'button_text' => fake()->sentence,
                    'content' => fake()->paragraph,
                ]);
            }
        });
    }
}
