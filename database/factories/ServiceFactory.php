<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Banner>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cover' => 'https://placehold.co/550x550',
            'slug' => fake()->slug,
            'price' => fake()->randomFloat(2, 100, 1000),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function ($service) {
            // Create translations for each language
            $languages = ['en', 'zh']; // Add more languages as needed

            foreach ($languages as $lang) {
                $service->translations()->create([
                    'language_code' => $lang,
                    'name' => fake()->sentence,
                    'description' => fake()->sentence,
                    'content' => fake()->paragraph,
                ]);
            }
        });
    }
}
