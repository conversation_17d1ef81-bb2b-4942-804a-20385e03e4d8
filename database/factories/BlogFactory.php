<?php

namespace Database\Factories;

use App\Models\Blog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Blog>
 */
class BlogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cover' => 'https://placehold.co/850x650',
            'cover_landscape' => 'https://placehold.co/1050x550',
            'slug' => fake()->slug,
            'author' => fake()->name,
            'published_at' => fake()->dateTime,
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Blog $blog) {
            $languages = ['en', 'zh'];

            foreach ($languages as $lang) {
                $blog->translations()->create([
                    'language_code' => $lang,
                    'title' => fake()->sentence,
                    'description' => fake()->sentence,
                    'content' => fake()->paragraph,
                ]);
            }
        });
    }
}
