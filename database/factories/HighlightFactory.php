<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Banner>
 */
class HighlightFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cover' => 'https://placehold.co/1000x1000',
            'cover_landscape' => 'https://placehold.co/1050x550',
            'slug' => fake()->slug,
            'external_url' => fake()->url,
            'sequence' => fake()->numberBetween(1, 100),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function ($highlight) {
            // Create translations for each language
            $languages = ['en', 'zh']; // Add more languages as needed

            foreach ($languages as $lang) {
                $highlight->translations()->create([
                    'language_code' => $lang,
                    'title' => fake()->sentence,
                    'description' => fake()->sentence,
                    'button_text' => fake()->sentence,
                    'content' => fake()->paragraph,
                ]);
            }
        });
    }
}
