<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cover' => 'https://placehold.co/850x850',
            'slug' => fake()->slug,
            'sequence' => fake()->numberBetween(1, 100),
            'section' => 'service',
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function (Category $category) {
            // Create translations for each language
            $languages = ['en', 'zh']; // Add more languages as needed

            foreach ($languages as $lang) {
                $category->translations()->create([
                    'language_code' => $lang,
                    'name' => fake()->sentence,
                    'description' => fake()->sentence,
                ]);
            }
        });
    }
}
