<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Admin>
 */
class CompanyFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'logo' => 'https://placehold.co/500x500',
            'legal_name' => fake()->name(),
            'name' => fake()->name(),
            'registration_no' => fake()->unique()->word(),
            'slug' => fake()->unique()->slug(),
        ];
    }
}
