<?php

namespace Database\Seeders;

use App\Models\Banner;
use App\Models\Blog;
use App\Models\Category;
use App\Models\Group;
use App\Models\Highlight;
use App\Models\User;
use Carbon\Carbon;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        \App\Models\Admin::factory()->create([
            'name' => 'Wenkang',
            'email' => '<EMAIL>',
        ]);

        $company = \App\Models\Company::factory()->create([
            'name' => 'hellonatorie',
            'legal_name' => 'hellonatorie',
            'registration_no' => '*********',
        ]);

        // timeslots future 30days
        for ($i = 0; $i < 30; $i++) {
            \App\Models\TimeSlot::factory()->create([
                'date' => Carbon::now()->addDays($i),
                'time' => '09:00AM - 10:00AM',
                'slotable_type' => 'App\Models\Company',
                'slotable_id' => $company->id,
            ]);

            \App\Models\TimeSlot::factory()->create([
                'date' => Carbon::now()->addDays($i),
                'time' => '10:00AM - 11:00AM',
                'slotable_type' => 'App\Models\Company',
                'slotable_id' => $company->id,
            ]);

            \App\Models\TimeSlot::factory()->create([
                'date' => Carbon::now()->addDays($i),
                'time' => '11:00AM - 12:00PM',
                'slotable_type' => 'App\Models\Company',
                'slotable_id' => $company->id,
            ]);

            \App\Models\TimeSlot::factory()->create([
                'date' => Carbon::now()->addDays($i),
                'time' => '12:00PM - 01:00PM',
                'slotable_type' => 'App\Models\Company',
                'slotable_id' => $company->id,
            ]);
        }

        User::factory(10)->create();
        Banner::factory(5)->create();
        Highlight::factory(5)->create();
        Blog::factory(5)->create();

        // Create categories with services
        $serviceNames = [
            'Recruitment Process Outsourcing',
            'Temporary & Contract Staffing',
            'Salary Benchmarking',
            'Executive Compensation Consulting',
            'HR Policy Development',
            'Employment Law Compliance',
            'Change Management',
            'Team Building & Development',
        ];

        // Create 4 categories
        for ($i = 0; $i < 4; $i++) {
            $cat = Category::factory()->create();
            $group = Group::factory()->create();
            // Create 2 services per category
            for ($j = 0; $j < 2; $j++) {
                $serviceIndex = $i * 2 + $j;
                if ($serviceIndex < count($serviceNames)) {
                    $service = \App\Models\Service::factory()->create([
                        'category_id' => $cat->id,
                        'company_id' => $company->id,
                        'group_id' => $group->id,
                        'price' => fake()->randomFloat(2, 100, 1000),
                    ]);

                    // Create translations for the service
                    $service->translations()->create([
                        'language_code' => 'en',
                        'name' => $serviceNames[$serviceIndex],
                        'description' => fake()->sentence,
                        'content' => fake()->paragraph,
                    ]);

                    $service->translations()->create([
                        'language_code' => 'zh',
                        'name' => $serviceNames[$serviceIndex].' (中文)',
                        'description' => fake()->sentence,
                        'content' => fake()->paragraph,
                    ]);
                }
            }
        }

        $languages = [
            ['flag' => '/dashboard-assets/media/flags/united-states.svg', 'name' => 'English', 'code' => 'en', 'is_active' => true],
            ['flag' => '/dashboard-assets/media/flags/china.svg', 'name' => 'Chinese', 'code' => 'zh', 'is_active' => true],
        ];

        foreach ($languages as $language) {
            \App\Models\Language::create($language);
        }
    }
}
