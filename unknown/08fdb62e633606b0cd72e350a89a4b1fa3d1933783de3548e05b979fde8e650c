<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;

class UserController extends Controller
{
    public function index()
    {
        return view('admin.user.index');
    }

    public function query()
    {
        $query = User::query()->with(['company']);

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.user.show', [$row->id]).'" class="btn btn-sm btn-info me-2">View</a>';
            $actions .= '<a href="'.route('admin.user.edit', [$row->id]).'" class="btn btn-sm btn-success">Edit</a>';

            return $actions;
        })->editColumn('identity_type', function ($row) {
            return $row->identity_type ?? '-';
        })->editColumn('identity_no', function ($row) {
            return $row->identity_no ?? '-';
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        return view('admin.user.create');
    }

    public function store(Photoshop $photoshop, Request $request)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:users',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone|unique:users,phone_e164,',
            'identity_type' => 'nullable|in:Malaysia IC,Passport Number',
            'identity_no' => 'nullable|required_if:identity_type,Malaysia IC,Passport Number|unique:users,identity_no,',
            'company_id' => 'nullable|exists:companies,id',
        ])->validate();

        $user = new User;
        $user->fill($request->all());
        $user->identity_type = $request->identity_type;
        $user->identity_no = $request->identity_no;
        $user->email_verified_at = $request->has('email_verified_at') ? ($user->email_verified_at ?? now()) : null;
        $user->phone_e164_verified_at = $request->has('phone_e164_verified_at') ? ($user->phone_e164_verified_at ?? now()) : null;
        $user->company_id = $request->company_id;

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $user->avatar = asset('storage/'.$filename);
        }

        $user->password = env('APP_NAME').date('Y');
        $user->save();

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.user');
    }

    public function show(User $user)
    {
        return view('admin.user.show', compact('user'));
    }

    public function edit(User $user)
    {
        return view('admin.user.edit', compact('user'));
    }

    public function update(Photoshop $photoshop, Request $request, User $user)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:users,email,'.$user->id,
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone|unique:users,phone_e164,'.$user->id,
            'identity_type' => 'nullable|in:Malaysia IC,Passport Number',
            'identity_no' => 'nullable|required_if:identity_type,Malaysia IC,Passport Number|unique:users,identity_no,'.$user->id,
            'company_id' => 'nullable|exists:companies,id',
        ])->validate();

        $user->fill($request->all());
        $user->identity_type = $request->identity_type;
        $user->identity_no = $request->identity_no;
        $user->email_verified_at = $request->has('email_verified_at') ? ($user->email_verified_at ?? now()) : null;
        $user->phone_e164_verified_at = $request->has('phone_e164_verified_at') ? ($user->phone_e164_verified_at ?? now()) : null;
        $user->company_id = $request->company_id;

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $user->avatar = asset('storage/'.$filename);
        }

        $user->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.user.edit', $user);
    }

    public function delete(User $user)
    {
        $user->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.user');
    }

    public function resetPassword(User $user)
    {
        Password::sendResetLink(['email' => $user->email]);
        Session::flash('alert-success', 'Email reset password sent');

        return redirect()->back();
    }
}
