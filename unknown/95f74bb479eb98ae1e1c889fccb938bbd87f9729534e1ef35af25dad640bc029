<?php

namespace App\Http\Controllers\Admin;

use App\Models\Payment;
use Yajra\DataTables\DataTables;

class PaymentController extends Controller
{
    public function index()
    {
        return view('admin.payment.index');
    }

    public function query()
    {
        $query = Payment::query();

        $result = DataTables::of($query)->addColumn('payable_id', function ($row) {
            if ($row->payable_type === 'App\Models\Booking') {
                return '<a href="'.route('admin.booking.show', [$row->payable_id]).'" target="_blank">'.$row->payable_id.'</a>';
            }
        })->addColumn('status', function ($row) {
            if ($row->failed_at) {
                return '<span class="badge badge-light-danger">Failed At '.$row->failed_at.'</span>';
            }

            if ($row->paid_at) {
                return '<span class="badge badge-light-success">Paid At '.$row->paid_at.'</span>';
            }

            return '<span class="badge badge-light-warning">Pending</span>';
        })->rawColumns(['payable_id', 'status'])->make(true);

        return $result;
    }
}
