<?php

namespace App\Http\Controllers\Public;

use App\Models\Product;

class ProductController extends Controller
{
    public function index()
    {
        $products = Product::query()->isActive()->get();

        return view('public.product.index', compact('products'));
    }

    public function show(Product $product)
    {
        $products = Product::query()->isActive()->get();

        return view('public.product.show', compact('product', 'products'));
    }
}
