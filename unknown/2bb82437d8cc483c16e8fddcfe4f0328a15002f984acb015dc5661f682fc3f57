$(document).ready(function () {
    const inputs = document.querySelectorAll(".phone_e164");
    inputs.forEach(function (input) {
        window.intlTelInput(input, {
            containerClass: "is-invalid",
            utilsScript:
                "/dashboard-assets/plugins/custom/intl-tel-input/utils.js",
            onlyCountries: ["my", "th", "sg", "cn"],
            showSelectedDialCode: true,
            hiddenInput: function (telInputName) {
                return {
                    phone: telInputName,
                    country: telInputName + "_country",
                };
            },
        });
    });

    window.setPhoneNumber = function (phoneNumber) {
        itiInstances.forEach(function (iti) {
            iti.setNumber(phoneNumber);
        });
    };

    $("select[name='identity_type']").change(function () {
        if ($(this).val() === "Malaysia IC") {
            Inputmask({ mask: "999999-99-9999" }).mask(
                "input[name='identity_no']"
            );
            Inputmask({ mask: "999999-99-9999" }).mask(
                "input[name='identity_no_confirmation']"
            );

            $("select[name='nationality']").val("Malaysian").trigger("change");
            $("select[name='nationality']").prop("disabled", true);
        } else {
            Inputmask({ mask: null }).mask("input[name='identity_no']");
            Inputmask({ mask: null }).mask(
                "input[name='identity_no_confirmation']"
            );

            $("select[name='nationality']").prop("disabled", false);
        }
    });

    $("select[name='identity_type']").trigger("change");

    // Prevent Form to be submit multiple times
    $("form").on("submit", function (e) {
        const submitButton = $(this).find("button[type='submit']");

        // Check for a custom attribute
        if (submitButton.attr("data-submitting") === "true") {
            e.preventDefault();
            return false;
        }

        // Set the custom attribute
        submitButton.attr("data-submitting", "true");

        // Update button UI
        submitButton.addClass("is-loading");
    });
});
