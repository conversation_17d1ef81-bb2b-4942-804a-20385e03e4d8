<?php

namespace App\Http\Controllers\Employee;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class MeController extends Controller
{
    public function editPassword()
    {
        return view('employee.me.edit-password');
    }

    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        Validator::make($request->all(), [
            'password' => 'current_password',
            'new_password' => ['required', 'confirmed', Password::min(8)],
        ])->validate();

        $user->password = $request->get('new_password');
        $user->save();

        Session::flash('alert-success', 'Successfully updated');

        return redirect()->route('employee.me.edit-password');
    }
}
