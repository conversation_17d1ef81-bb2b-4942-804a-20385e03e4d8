name: Auto Deploy to Heartbyte EC2
on:
  push:
    branches:
      - main
jobs:
  Deployment:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Setup
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: ubuntu
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/${{ github.event.repository.name }}
            git pull
