<?php

if (! function_exists('getTranslation')) {
    /**
     * Get the translation for a morphable model (Product, Category, etc.) based on the current language,
     * and fallback to the default language if the translation is not available.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model  The model instance that has translations (e.g., Product, Category, etc.)
     * @param  string  $column  The column to translate (e.g., 'name', 'description', etc.)
     * @return string|null The translated value, or fallback to the default language if the current language is unavailable.
     */
    function getTranslation($model, $column, $lang = null)
    {
        // Get the current app language (e.g., 'en', 'fr', 'es')
        $currentLang = $lang ? $lang : app()->getLocale();

        // Try to get the translation for the current language
        $translation = $model->translations->where('language_code', $currentLang)->first();

        // If a translation exists and the column is filled, return the value
        if ($translation && ! empty($translation->$column)) {
            return $translation->$column;
        }

        // If no translation for the current language, fallback to default language
        $defaultLang = config('app.fallback_locale');

        // Try to get the translation for the fallback language
        $fallbackTranslation = $model->translations->where('language_code', $defaultLang)->first();

        // Return the fallback translation if available, otherwise null
        return $fallbackTranslation->$column ?? null;
    }
}
