<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class TimeSlot extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'date',
        'time',
        'is_booked',
    ];

    protected $casts = [
        'date' => 'date',
    ];
    /***************************************
     * Relationships
     ***************************************/

    /***************************************
     * Relationships
     ***************************************/
    public function slotable(): MorphTo
    {
        return $this->morphTo();
    }

    public function booking(): HasOne
    {
        return $this->hasOne(Booking::class)->latestOfMany();
    }

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/

    /***************************************
     * Query Scopes
     ***************************************/

    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
