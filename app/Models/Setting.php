<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Setting extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'group',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'value' => 'json',
    ];

    /**
     * Get a setting by key
     *
     * @param  mixed  $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        if (! $setting) {
            return $default;
        }

        return $setting->value;
    }

    /**
     * Get the QR code image URL
     *
     * @return string|null
     */
    public static function getQrCodeImageUrl()
    {
        $paymentMethods = self::get('payment_methods', []);

        if (isset($paymentMethods['qr_code_image_path']) && ! empty($paymentMethods['qr_code_image_path'])) {
            return route('admin.setting.payment-methods.qr-code');
        }

        return null;
    }

    /**
     * Set a setting by key
     *
     * @param  mixed  $value
     * @return Setting
     */
    public static function set(string $key, $value, string $group = 'general')
    {
        $setting = self::firstOrNew(['key' => $key]);
        $setting->value = $value;
        $setting->group = $group;
        $setting->save();

        return $setting;
    }
}
