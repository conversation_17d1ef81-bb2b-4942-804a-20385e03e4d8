<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Leave extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'start_date',
        'end_date',
        'duration',
        'is_half_day',
        'remark',
        'employee_id',
        'company_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'duration' => 'float',
        'is_half_day' => 'boolean',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function approver()
    {
        return $this->morphTo();
    }

    public function rejecter()
    {
        return $this->morphTo();
    }

    public function creator()
    {
        return $this->morphTo();
    }

    /**
     * Get the attachments for the leave.
     */
    public function attachments()
    {
        return $this->hasMany(LeaveAttachment::class);
    }

    protected $dispatchesEvents = [
        'created' => \App\Events\Leave\Created::class,
    ];
}
