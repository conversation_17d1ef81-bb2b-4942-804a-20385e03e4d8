<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Group extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'slug',
        'is_active',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function translations(): MorphMany
    {
        return $this->morphMany(Translation::class, 'translatable');
    }

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsActive(Builder $query): void
    {
        $query->where('is_active', true);
    }
    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
