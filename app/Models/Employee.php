<?php

namespace App\Models;

use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Contracts\Auditable;

class Employee extends Authenticatable implements Auditable, CanResetPassword
{
    use HasApiTokens, HasFactory, HasUlids, Notifiable, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone_e164',
        'gender',
        'employment_status',
        'identity_type',
        'identity_no',
        'is_owner',
        'yearly_annual_leave_entitlement',
        'yearly_medical_leave_entitlement',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'password' => 'hashed',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }
    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/
    protected function avatar(): Attribute
    {
        return Attribute::make(
            get: fn ($avatar) => $avatar ?? asset('/dashboard-assets/media/avatars/blank.png'),
            set: fn ($avatar) => $avatar
        );
    }
    /***************************************
     * Query Scopes
     ***************************************/

    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/

    /**
     * Get available annual leave balance
     *
     * @param  bool  $includePending  Whether to include pending leave applications in the calculation
     * @return float
     */
    public function getAvailableAnnualLeave($includePending = true)
    {
        $balance = $this->yearly_annual_leave_entitlement - $this->taken_annual_leave;

        if ($includePending) {
            $pendingLeave = $this->leaves()
                ->where('type', 'AL')
                ->whereNull('approved_at')
                ->whereNull('rejected_at')
                ->sum('duration');

            $balance -= $pendingLeave;
        }

        return max(0, $balance);
    }

    /**
     * Get available medical leave balance
     *
     * @param  bool  $includePending  Whether to include pending leave applications in the calculation
     * @return float
     */
    public function getAvailableMedicalLeave($includePending = true)
    {
        $balance = $this->yearly_medical_leave_entitlement - $this->taken_medical_leave;

        if ($includePending) {
            $pendingLeave = $this->leaves()
                ->where('type', 'MC')
                ->whereNull('approved_at')
                ->whereNull('rejected_at')
                ->sum('duration');

            $balance -= $pendingLeave;
        }

        return max(0, $balance);
    }

    // protected $dispatchesEvents = [
    //     'created' => \App\Events\Employee\Created::class,
    // ];
}
