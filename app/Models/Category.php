<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Category extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'slug',
        'sequence',
        'is_active',
        'section',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }

    public function translations(): MorphMany
    {
        return $this->morphMany(Translation::class, 'translatable');
    }

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/
    protected function cover(): Attribute
    {
        return Attribute::make(
            get: fn ($cover) => $cover ?? asset('/dashboard-assets/media/avatars/blank.png'),
            set: fn ($cover) => $cover
        );
    }

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsService(Builder $query): void
    {
        $query->where('section', 'service');
    }

    public function scopeIsActive(Builder $query): void
    {
        $query->where('is_active', true);
    }
    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
