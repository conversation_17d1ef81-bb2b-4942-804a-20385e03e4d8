<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Company extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'registration_no',
        'legal_name',
        'name',
        'description',
        'bank_name',
        'bank_no',
        'bank_swift_code',
        'main_pic_name',
        'main_pic_phone_e164',
        'main_pic_email',
        'main_pic_address1',
        'main_pic_address2',
        'main_pic_country',
        'main_pic_state',
        'main_pic_city',
        'main_pic_postcode',
        'is_active',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function timeSlots(): MorphMany
    {
        return $this->morphMany(TimeSlot::class, 'slotable');
    }

    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/
    protected function logo(): Attribute
    {
        return Attribute::make(
            get: fn ($logo) => $logo ?? asset('/dashboard-assets/media/avatars/blank.png'),
            set: fn ($logo) => $logo
        );
    }

    protected function mainPicAddress(): Attribute
    {
        return Attribute::make(
            get: fn () => ($this->main_pic_address1 ? $this->main_pic_address1.',' : '').($this->main_pic_address2 ? $this->main_pic_address2.',' : '').($this->main_pic_city ? $this->main_pic_city.',' : '').($this->main_pic_state ? $this->main_pic_state.',' : '').($this->main_pic_postcode ? $this->main_pic_postcode.',' : '').($this->main_pic_country ? $this->main_pic_country : ''),
        );
    }

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsActive(Builder $query): void
    {
        $query->where('is_active', true);
    }
    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
