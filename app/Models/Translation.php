<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Translation extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = ['language_code', 'name', 'title', 'button_text', 'description', 'content'];

    /***************************************
     * Relationships
     ***************************************/
    public function translatable(): MorphTo
    {
        return $this->morphTo();
    }
    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/

    /***************************************
     * Query Scopes
     ***************************************/

    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
