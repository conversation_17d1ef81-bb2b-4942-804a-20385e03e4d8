<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class LeaveAttachment extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'leave_id',
        'original_filename',
        'filename',
        'file_path',
        'mime_type',
        'file_size',
    ];

    /**
     * Get the leave that owns the attachment.
     */
    public function leave(): BelongsTo
    {
        return $this->belongsTo(Leave::class);
    }

    /**
     * Get the full path to the file.
     */
    public function getFullPathAttribute(): string
    {
        return storage_path('app/'.$this->file_path);
    }

    /**
     * Get the formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2).' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2).' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2).' KB';
        } else {
            return $bytes.' bytes';
        }
    }

    /**
     * Get the file icon based on mime type.
     */
    public function getFileIconAttribute(): string
    {
        $mime = $this->mime_type;

        if (strpos($mime, 'image/') === 0) {
            return 'fa-file-image';
        } elseif (strpos($mime, 'application/pdf') === 0) {
            return 'fa-file-pdf';
        } elseif (strpos($mime, 'application/msword') === 0 || strpos($mime, 'application/vnd.openxmlformats-officedocument.wordprocessingml') === 0) {
            return 'fa-file-word';
        } elseif (strpos($mime, 'application/vnd.ms-excel') === 0 || strpos($mime, 'application/vnd.openxmlformats-officedocument.spreadsheetml') === 0) {
            return 'fa-file-excel';
        } elseif (strpos($mime, 'application/vnd.ms-powerpoint') === 0 || strpos($mime, 'application/vnd.openxmlformats-officedocument.presentationml') === 0) {
            return 'fa-file-powerpoint';
        } elseif (strpos($mime, 'text/') === 0) {
            return 'fa-file-alt';
        } elseif (strpos($mime, 'application/zip') === 0 || strpos($mime, 'application/x-rar') === 0) {
            return 'fa-file-archive';
        } else {
            return 'fa-file';
        }
    }
}
