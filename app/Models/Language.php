<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Language extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'name',
        'code',
        'is_active',
    ];

    /***************************************
     * Relationships
     ***************************************/

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsActive(Builder $query): void
    {
        $query->where('is_active', true);
    }
    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
