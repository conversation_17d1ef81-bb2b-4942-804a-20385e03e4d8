<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Booking extends Model implements Auditable
{
    use HasFactory, HasUlids, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $casts = [
        'user_meta' => 'object',
        'time_slot_meta' => 'object',
        'service_meta' => 'object',
        'date' => 'date',
        'failed_at' => 'datetime',
        'paid_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'accepted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /***************************************
     * Relationships
     ***************************************/
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function timeSlot(): BelongsTo
    {
        return $this->belongsTo(TimeSlot::class);
    }

    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    public function payment(): MorphOne
    {
        return $this->morphOne(Payment::class, 'payable');
    }
    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function () {
                // cancelled_at,failed_at, paid_at, prepared_at, shipped_at, delivered_at
                if ($this->cancelled_at) {
                    return 'Cancelled';
                }

                if ($this->failed_at) {
                    return 'Failed';
                }

                if ($this->completed_at) {
                    return 'Completed';
                }

                if ($this->accepted_at) {
                    return 'Accepted';
                }

                if ($this->paid_at) {
                    return 'Paid';
                }

                return 'Pending';
            }
        );
    }

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsCancelled(Builder $query): Builder
    {
        return $query->whereNotNull('cancelled_at');
    }

    public function scopeIsFailed(Builder $query): Builder
    {
        return $query->whereNotNull('failed_at');
    }

    public function scopeIsPaid(Builder $query): Builder
    {
        return $query->whereNotNull('paid_at')->whereNull('failed_at')->whereNull('cancelled_at');
    }

    public function scopeIsPending(Builder $query): Builder
    {
        return $query->whereNull('paid_at')->whereNull('failed_at')->whereNull('cancelled_at');
    }

    public function scopeIsCompleted(Builder $query): Builder
    {
        return $query->whereNotNull('completed_at')->whereNull('failed_at')->whereNull('cancelled_at');
    }

    public function scopeIsAccepted(Builder $query): Builder
    {
        return $query->whereNotNull('accepted_at')->whereNull('failed_at')->whereNull('cancelled_at')->whereNull('completed_at')->whereNotNull('paid_at');
    }

    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/

    protected static function booted(): void
    {
        static::creating(function (Booking $booking) {
            $increment = 1;
            $no = 'BK'.str_pad(self::count() + $increment, 5, '0', STR_PAD_LEFT);
            $booking->no = 'BK'.str_pad(self::count() + $increment, 5, '0', STR_PAD_LEFT);

            while (self::where('no', $no)->exists()) {
                $increment++;
                try {
                    $booking->no = 'BK'.str_pad(self::count() + $increment, 5, '0', STR_PAD_LEFT);
                    break;
                } catch (\Exception $e) {
                    if ($increment > 100) {
                        throw $e;
                    }
                }
            }
        });
    }

    protected $dispatchesEvents = [
        'created' => \App\Events\Booking\Created::class,
    ];
}
