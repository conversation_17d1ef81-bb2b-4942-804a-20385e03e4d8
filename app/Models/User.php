<?php

namespace App\Models;

use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class User extends Authenticatable implements Auditable, CanResetPassword, MustVerifyEmail
{
    use HasFactory, HasUlids, Notifiable, \OwenIt\Auditing\Auditable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone_e164',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'identity_no',
        'identity_type',
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_e164_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /***************************************
     * Relationships
     ***************************************/
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /***************************************
     * Mutators & Casting
     ***************************************/

    /***************************************
     * Attributes/Accessors
     ***************************************/
    protected function avatar(): Attribute
    {
        return Attribute::make(
            get: fn ($avatar) => $avatar ?? asset('/dashboard-assets/media/avatars/blank.png'),
            set: fn ($avatar) => $avatar
        );
    }

    /***************************************
     * Query Scopes
     ***************************************/
    public function scopeIsActive(Builder $query): void
    {
        $query->where('is_active', true);
    }
    /***************************************
     * Boot Method
     ***************************************/

    /***************************************
     * Custom Methods
     ***************************************/
}
