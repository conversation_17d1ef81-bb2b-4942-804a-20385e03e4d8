<?php

namespace App\Exports\Company\TimeSlot;

use App\Models\Company;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class All implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithTitle
{
    /**
     * @return \Illuminate\Support\Collection
     */
    use Exportable;

    private $date;

    private $company;

    public function __construct(Carbon $date, Company $company)
    {
        $this->date = $date;
        $this->company = $company;
    }

    public function title(): string
    {
        return 'Time Slot';
    }

    public function collection()
    {
        $timeSlots = $this->company->timeSlots()
            ->whereYear('date', $this->date->year)
            ->whereMonth('date', $this->date->month)
            ->orderBy('date', 'asc')->get();
        $data = [];

        if (count($timeSlots) > 0) {

            foreach ($timeSlots as $timeSlot) {
                $data[] = [
                    'id' => $timeSlot->id,
                    'date' => \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($timeSlot->date->format('d/m/Y')),
                    'time' => $timeSlot->time,
                    'is_booked' => $timeSlot->is_booked ? '1' : '0',
                ];
            }
        }

        return collect($data);
    }

    public function headings(): array
    {
        return ['ID', 'Date', 'Time', 'Is Booked'];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }
}
