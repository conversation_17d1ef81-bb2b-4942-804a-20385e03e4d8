<?php

namespace App\Notifications\User\Booking;

use App\Models\Booking;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UpdateStatus extends Notification
{
    use Queueable;

    protected $booking;

    /**
     * Create a new notification instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {

        // invoice
        $html = view('pdf.booking-invoice', ['booking' => $this->booking])->render();
        $pdf = Pdf::loadHTML($html)->setPaper('a4', 'landscape')->setWarnings(false);

        return (new MailMessage)
            ->subject('Booking #'.$this->booking->no.' current status is '.$this->booking->status)
            ->markdown('mail.booking.update-status', ['booking' => $this->booking])
            ->attachData($pdf->output(), 'invoice-'.$this->booking->no.'.pdf', [
                'mime' => 'application/pdf',
            ]);
    }
}
