<?php

namespace App\Notifications\Employee\Leave;

use App\Models\Leave;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class Rejected extends Notification
{
    use Queueable;

    protected $leave;

    /**
     * Create a new notification instance.
     */
    public function __construct(Leave $leave)
    {
        $this->leave = $leave;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mail = (new MailMessage)
            ->subject('Your Leave Request has been Rejected')
            ->line('Your leave request has been rejected.');

        if (! empty($this->leave->reject_reason)) {
            $mail->line('Reason for rejection:')
                ->line($this->leave->reject_reason);
        }

        $mail->line('Please check the leave request details below:')
            ->action('Check Leave', route('employee.leave.show', [$this->leave->company, $this->leave->id]));

        return $mail;
    }
}
