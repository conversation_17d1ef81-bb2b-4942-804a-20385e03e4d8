<?php

namespace App\Listeners\Leave;

use App\Events\Leave\Created;
use App\Notifications\Admin\LeaveCreated;
use Illuminate\Support\Facades\Notification;

class NotifyAdmin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Created $event): void
    {
        $emails = [
            '<EMAIL>',
        ];

        $leave = $event->leave;

        Notification::route('mail', $emails)->notify(new LeaveCreated($leave));
    }
}
