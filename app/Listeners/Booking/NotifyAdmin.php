<?php

namespace App\Listeners\Booking;

use App\Events\Booking\Created;
use App\Notifications\Admin\BookingCreated;
use Illuminate\Support\Facades\Notification;

class NotifyAdmin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Created $event): void
    {
        $emails = [
            '<EMAIL>',
        ];

        $booking = $event->booking;

        Notification::route('mail', $emails)->notify(new BookingCreated($booking));
    }
}
