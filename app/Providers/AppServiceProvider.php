<?php

namespace App\Providers;

use App\Models\Employee;
use App\Models\Leave;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('employee.*', function ($view) {
            $company = request()->route('company');
            $view->with('company', $company);
        });

        View::composer('admin.*', function ($view) {
            $pendingLeaveCount = Leave::whereNull('approved_at')
                ->whereNull('rejected_at')
                ->count();

            $view->with('pendingLeaveCount', $pendingLeaveCount);
        });

        ResetPassword::createUrlUsing(function ($user, string $token) {
            return match (true) {
                $user instanceof Employee => route('employee.password.reset')."/{$token}?email={$user->email}",
                default => throw new \Exception('Invalid user type'),
            };
        });
    }
}
