<?php

namespace App\Services;

use Carbon\Carbon;
use Google\Client;
use Google\Service\Calendar;
use Google\Service\Calendar\Event;
use Illuminate\Support\Facades\Log;

class GoogleCalendar
{
    public function createEvent($booking)
    {
        try {
            $client = new Client;
            $full_path = storage_path(env('GOOGLE_APPLICATION_CREDENTIALS'));
            $client->setAuthConfig($full_path);
            $client->useApplicationDefaultCredentials();
            $client->addScope(Calendar::CALENDAR);

            $service = new Calendar($client);
            $colors = $service->colors->get();
            // dd($colors);
            // 11:00AM - 12:00PM
            $time = explode(' - ', $booking->timeSlot->time);
            $startDateTime = Carbon::createFromFormat(
                'Y-m-d h:i A',
                $booking->date->format('Y-m-d').' '.$time[0],
                'Asia/Kuala_Lumpur'
            );

            $endDateTime = Carbon::createFromFormat(
                'Y-m-d h:i A',
                $booking->date->format('Y-m-d').' '.$time[1],
                'Asia/Kuala_Lumpur'
            );

            $booking->calendar_event_id = Carbon::now()->timestamp;
            $booking->save();

            $event = new Event([
                'id' => $booking->calendar_event_id,
                'summary' => env('APP_NAME').' - '.$booking->service_meta->name,
                'description' => 'Thanks for booking with us. We look forward to seeing you soon.',
                'start' => [
                    'dateTime' => $startDateTime->toIso8601String(),
                    'timeZone' => 'Asia/Kuala_Lumpur',
                ],
                'end' => [
                    'dateTime' => $endDateTime->toIso8601String(),
                    'timeZone' => 'Asia/Kuala_Lumpur',
                ],
                // background color
                'location' => $booking->google_meet_link,

            ]);

            $calendarId = env('GOOGLE_CALENDAR_ID');
            $event = $service->events->insert($calendarId, $event);

            // Logic to create an event in Google Calendar
            // This is a placeholder for the actual implementation
            return $event;
        } catch (\Exception $e) {
            // Handle the exception
            Log::error('Google Calendar Error: '.$e->getMessage());

            return null;
        }
    }

    public function deleteEvent($booking)
    {
        try {
            $client = new Client;
            $full_path = storage_path(env('GOOGLE_APPLICATION_CREDENTIALS'));
            $client->setAuthConfig($full_path);
            $client->useApplicationDefaultCredentials();
            $client->addScope(Calendar::CALENDAR);

            $service = new Calendar($client);
            $calendarId = env('GOOGLE_CALENDAR_ID');
            $service->events->delete($calendarId, $booking->calendar_event_id);
        } catch (\Exception $e) {
            // Handle the exception
            Log::error('Google Calendar Error: '.$e->getMessage());

            return null;
        }
    }
}
