<?php

namespace App\Http\Controllers\Employee;

use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password as PasswordRule;
use Str;

class ForgotPasswordController extends Controller
{
    public function __invoke()
    {
        return view('employee.forgot-password.index');
    }

    public function forgot(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => 'Please fill in email']);
        } else {

            $user = Employee::where('email', $request->get('email'))->first();

            if (! $user) {
                return redirect()->back()->withInput()->withErrors(['auth' => "Email doesn't exist"]);
            }

            $status = Password::broker('employees')->sendResetLink(
                $request->only('email')
            );
        }

        if ($status === Password::RESET_LINK_SENT) {
            return view('employee.forgot-password.request-password-thankyou');
        } else {
            return back()->withErrors(['email' => __($status)]);
        }
    }

    public function resetForm(Request $request)
    {
        $token = $request->token;

        return view('employee.forgot-password.reset', compact('token'));
    }

    public function reset(Request $request)
    {
        Validator::make($request->all(), [
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', PasswordRule::min(8)],
        ])->validate();

        Password::broker('employees')->reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->setRememberToken(Str::random(60));

                $user->save();
            }
        );

        return view('employee.forgot-password.reset-password-thankyou');
    }
}
