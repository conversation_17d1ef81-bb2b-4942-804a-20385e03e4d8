<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Leave;
use App\Models\LeaveAttachment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class LeaveController extends Controller
{
    public function index(Company $company)
    {
        return view('employee.leave.index', compact('company'));
    }

    public function create(Company $company)
    {
        return view('employee.leave.create', compact('company'));
    }

    public function store(Company $company, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:AL,MC,NPL',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'is_half_day' => 'boolean',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        }

        $employee = Auth::user();
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        if ($startDate->isSameDay($endDate)) {
            $duration = 1;
        } else {
            $duration = $startDate->diffInDays($endDate) + 1;
        }

        if ($request->has('is_half_day') && $request->is_half_day) {
            $duration = 0.5;
        }

        $pendingAnnualLeave = Leave::where('employee_id', $employee->id)
            ->where('type', 'AL')
            ->whereNull('approved_at')
            ->whereNull('rejected_at')
            ->sum('duration');

        $pendingMedicalLeave = Leave::where('employee_id', $employee->id)
            ->where('type', 'MC')
            ->whereNull('approved_at')
            ->whereNull('rejected_at')
            ->sum('duration');

        if ($request->type === 'AL') {
            $availableBalance = max(0, $employee->yearly_annual_leave_entitlement - $employee->taken_annual_leave - $pendingAnnualLeave);
            if ($availableBalance < $duration) {
                return redirect()->back()->withInput()->withErrors(['error' => "Not enough annual leave balance. Available: {$availableBalance} days, Requested: {$duration} days"]);
            }
        } elseif ($request->type === 'MC') {
            $availableBalance = max(0, $employee->yearly_medical_leave_entitlement - $employee->taken_medical_leave - $pendingMedicalLeave);
            if ($availableBalance < $duration) {
                return redirect()->back()->withInput()->withErrors(['error' => "Not enough medical leave balance. Available: {$availableBalance} days, Requested: {$duration} days"]);
            }
        }

        $leave = new Leave;
        $leave->type = $request->type;
        $leave->start_date = $startDate;
        $leave->end_date = $endDate;
        $leave->duration = $duration;
        $leave->is_half_day = $request->has('is_half_day') ? true : false;
        $leave->remark = $request->remark;
        $leave->employee_id = $employee->id;
        $leave->company_id = $company->id;
        $leave->creator_type = 'App\Models\Employee';
        $leave->creator_id = $employee->id;
        $leave->save();

        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $filename = Str::ulid().'.'.$file->getClientOriginalExtension();

                $path = $file->storeAs('leave_attachments/'.$leave->id, $filename);

                $attachment = new LeaveAttachment;
                $attachment->leave_id = $leave->id;
                $attachment->original_filename = $file->getClientOriginalName();
                $attachment->filename = $filename;
                $attachment->file_path = $path;
                $attachment->mime_type = $file->getMimeType();
                $attachment->file_size = $file->getSize();
                $attachment->save();
            }
        }

        Session::flash('alert-success', 'Leave application submitted successfully');

        return redirect()->route('employee.leave', $company);
    }

    public function show(Company $company, Leave $leave)
    {
        $employee = Auth::user();

        if (! $employee->is_owner && $leave->employee_id !== $employee->id) {
            abort(403, 'Unauthorized action.');
        }

        if ($leave->company_id !== $company->id) {
            abort(404, 'Leave not found.');
        }

        return view('employee.leave.show', compact('company', 'leave'));
    }

    public function query(Company $company)
    {
        $employee = Auth::user();
        $leaves = Leave::where('employee_id', $employee->id)
            ->where('company_id', $company->id)
            ->select('*');

        return DataTables::of($leaves)
            ->addColumn('actions', function ($row) use ($company) {
                $actions = '<div class="d-flex justify-content-end flex-shrink-0">';

                $actions .= '<a href="'.route('employee.leave.show', [$company, $row]).'" class="btn btn-sm btn-info mx-1">Show</a>';

                $actions .= '</div>';

                return $actions;
            })->editColumn('start_date', function ($row) {
                return $row->start_date->format('Y-m-d');
            })
            ->editColumn('end_date', function ($row) {
                return $row->end_date->format('Y-m-d');
            })
            ->editColumn('duration', function ($row) {
                return $row->duration;
            })
            ->editColumn('status', function ($row) {
                if ($row->approved_at) {
                    return '<span class="badge badge-light-success">Approved</span>';
                } elseif ($row->rejected_at) {
                    return '<span class="badge badge-light-danger">Rejected</span>';
                } else {
                    return '<span class="badge badge-light-warning">Pending</span>';
                }
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at;
            })
            ->rawColumns(['actions', 'status'])
            ->make(true);
    }

    public function downloadAttachment(Company $company, Leave $leave, LeaveAttachment $attachment)
    {
        $employee = Auth::user();

        if (! $employee->is_owner && $leave->employee_id !== $employee->id) {
            abort(403, 'Unauthorized action.');
        }

        if ($leave->company_id !== $company->id) {
            abort(404, 'Leave not found.');
        }

        if ($attachment->leave_id !== $leave->id) {
            abort(404, 'Attachment not found.');
        }

        return Storage::download($attachment->file_path, $attachment->original_filename);
    }

    public function previewAttachment(Company $company, Leave $leave, LeaveAttachment $attachment)
    {
        $employee = Auth::user();

        if (! $employee->is_owner && $leave->employee_id !== $employee->id) {
            abort(403, 'Unauthorized action.');
        }

        if ($leave->company_id !== $company->id) {
            abort(404, 'Leave not found.');
        }

        if ($attachment->leave_id !== $leave->id) {
            abort(404, 'Attachment not found.');
        }

        if (! Storage::exists($attachment->file_path)) {
            abort(404, 'File not found.');
        }

        $file = Storage::get($attachment->file_path);

        return response($file, 200)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Disposition', 'inline; filename="'.$attachment->original_filename.'"');
    }
}
