<?php

namespace App\Http\Controllers\Employee;

use App\Models\Company;
use App\Models\Leave;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __invoke(Company $company)
    {
        return view('employee.dashboard', compact('company'));
    }

    public function switch()
    {
        return redirect()->route('employee', Auth::user()->company);
    }

    /**
     * Get approved leaves for the calendar
     */
    public function getLeaves(Company $company): JsonResponse
    {
        $employee = Auth::user();
        $events = [];

        if ($employee->is_owner) {
            $leaves = Leave::where('company_id', $company->id)
                ->whereNotNull('approved_at')
                ->get();

            foreach ($leaves as $leave) {
                $url = route('employee.leave.show', [$company, $leave]);

                $events[] = [
                    'id' => $leave->id,
                    'title' => $leave->employee->name.' - '.($leave->type === 'AL' ? 'Annual Leave' : ($leave->type === 'MC' ? 'Medical Leave' : 'No Pay Leave')).($leave->remark ? '  With Remark : '.$leave->remark : ''),
                    'start' => $leave->start_date->format('Y-m-d'),
                    'end' => $leave->end_date->addDay()->format('Y-m-d'), // Add a day to make it inclusive
                    'url' => $url,
                    'backgroundColor' => $leave->type === 'AL' ? '#20c997' : ($leave->type === 'MC' ? '#F64E60' : '#FFA800'),
                    'borderColor' => $leave->type === 'AL' ? '#20c997' : ($leave->type === 'MC' ? '#F64E60' : '#FFA800'),
                    'textColor' => '#ffffff',
                    'allDay' => true,
                ];
            }
        } else {
            // For regular employees, only show their own leaves
            $leaves = Leave::where('employee_id', $employee->id)
                ->where('company_id', $company->id)
                ->whereNotNull('approved_at')
                ->get();

            foreach ($leaves as $leave) {
                $events[] = [
                    'id' => $leave->id,
                    'title' => ($leave->type === 'AL' ? 'Annual Leave' : ($leave->type === 'MC' ? 'Medical Leave' : 'No Pay Leave')).($leave->remark ? '  With Remark : '.$leave->remark : ''),
                    'start' => $leave->start_date->format('Y-m-d'),
                    'end' => $leave->end_date->addDay()->format('Y-m-d'), // Add a day to make it inclusive
                    'url' => route('employee.leave.show', [$company, $leave]),
                    'backgroundColor' => $leave->type === 'AL' ? '#20c997' : ($leave->type === 'MC' ? '#F64E60' : '#FFA800'),
                    'borderColor' => $leave->type === 'AL' ? '#20c997' : ($leave->type === 'MC' ? '#F64E60' : '#FFA800'),
                    'textColor' => '#ffffff',
                    'allDay' => true,
                ];
            }
        }

        return response()->json($events);
    }
}
