<?php

namespace App\Http\Controllers\Employee;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    public function index()
    {
        return view('employee.login');
    }

    public function authenticate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => 'Please fill in required fields']);
        } else {
            $credentials = $request->only('email', 'password');
            if (Auth::attempt($credentials, $request->get('remember'))) {
                return redirect()->intended(route('employee', Auth::user()->company_id));
            } else {
                return redirect()->back()->withInput()->withErrors(['auth' => 'Incorrect Email or Password']);
            }
        }
    }

    public function logout()
    {
        Auth::logout();

        return redirect()->route('employee.login');
    }
}
