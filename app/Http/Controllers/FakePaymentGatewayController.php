<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class FakePaymentGatewayController extends Controller
{
    public function __invoke(Payment $payment)
    {

        if ($payment->failed_at || $payment->paid_at) {
            return redirect()->route('fake-payment.callback', $payment);
        }

        $paymentMethods = Setting::get('payment_methods', []);
        $qrCodeUrl = Setting::getQrCodeImageUrl();

        return view('fake-payment.index', compact('payment', 'paymentMethods', 'qrCodeUrl'));
    }

    public function action(Request $request, Payment $payment)
    {

        if ($payment->failed_at || $payment->paid_at) {
            return redirect()->route('fake-payment.callback', $payment);
        }

        if ($request->get('status') == 'paid') {
            if (! $request->hasFile('receipt')) {
                return redirect()->back()->withErrors(['receipt' => 'The receipt field is required when confirming payment.'])->withInput();
            }

            $validator = Validator::make($request->all(), [
                'receipt' => 'required|file|mimes:jpeg,png,jpg,gif,pdf|max:5120',
            ]);

            if ($validator->fails()) {
                Log::error('Payment validation failed', [
                    'errors' => $validator->errors()->toArray(),
                ]);

                return redirect()->back()->withErrors($validator)->withInput();
            }
        }
        if ($request->get('status') == 'paid') {
            try {
                if ($payment->receipt_path && Storage::exists($payment->receipt_path)) {
                    Storage::delete($payment->receipt_path);
                }

                $file = $request->file('receipt');
                $filename = Str::ulid().'.'.$file->getClientOriginalExtension();
                $path = $file->storeAs('payment_receipts', $filename);

                $payment->receipt_path = $path;
                $payment->receipt_original_filename = $file->getClientOriginalName();
                $payment->receipt_mime_type = $file->getMimeType();

                Log::info('Receipt uploaded successfully', [
                    'filename' => $filename,
                    'path' => $path,
                    'original_name' => $file->getClientOriginalName(),
                ]);

                $payment->paid_at = now();
                $payment->method = 'simulator';
                $payment->save();
            } catch (\Exception $e) {
                Log::error('Error processing payment', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return redirect()->back()->withErrors(['receipt' => 'Error processing payment: '.$e->getMessage()])->withInput();
            }

            $payable = $payment->payable;
            $payable->paid_at = now();
            $payable->save();
        } else {
            try {
                $payment->failed_at = now();
                $payment->method = 'simulator';
                $payment->save();

                $payable = $payment->payable;
                $payable->failed_at = now();
                $payable->save();

                if ($payable instanceof Booking) {
                    $payable->timeSlot->is_booked = false;
                    $payable->timeSlot->save();
                }
            } catch (\Exception $e) {
                Log::error('Error cancelling payment', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return redirect()->back()->withErrors(['general' => 'Error cancelling payment: '.$e->getMessage()])->withInput();
            }
        }

        Notification::route('mail', $payable->email)->notify(new \App\Notifications\User\Booking\UpdateStatus($payable));

        return redirect()->route('fake-payment.callback', $payment);
    }

    public function callback(Payment $payment)
    {
        return view('fake-payment.callback', compact('payment'));
    }

    public function serveReceipt(Payment $payment)
    {
        if (! $payment->receipt_path || ! Storage::exists($payment->receipt_path)) {
            abort(404, 'Receipt not found');
        }

        $file = Storage::get($payment->receipt_path);
        $mimeType = $payment->receipt_mime_type ?? 'application/octet-stream';
        $filename = $payment->receipt_original_filename ?? 'receipt.pdf';

        return response($file, 200)
            ->header('Content-Type', $mimeType)
            ->header('Content-Disposition', 'inline; filename="'.$filename.'"');
    }
}
