<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use App\Models\Employee;
use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;

class CompanyEmployeeController extends Controller
{
    public function index(Company $company)
    {
        return view('admin.company.employee.index', compact('company'));
    }

    public function query(Company $company)
    {
        $query = $company->employees();

        $result = DataTables::of($query)->addColumn('actions', function ($row) use ($company) {
            $actions = '<a href="'.route('admin.company.employee.edit', [$company, $row->id]).'" class="btn btn-sm btn-success">Edit</a>';

            return $actions;
        })->addColumn('info', function ($row) {
            $info = '<strong>Name:</strong> '.$row->name.'<br>';
            $info .= '<strong>Email:</strong> '.$row->email.'<br>';
            $info .= '<strong>Phone:</strong> '.$row->phone_e164.'<br>';
            $info .= '<strong>Gender:</strong> '.$row->gender.'<br>';
            $info .= '<strong>Identity Type:</strong> '.$row->identity_type.'<br>';
            $info .= '<strong>Identity No:</strong> '.$row->identity_no;

            return $info;
        })->editColumn('leave_info', function ($row) {
            $info = '<strong>Yearly Annual Leave Entitlement:</strong> '.$row->yearly_annual_leave_entitlement.'<br>';
            $info .= '<strong>Yearly Medical Leave Entitlement:</strong> '.$row->yearly_medical_leave_entitlement.'<br>';

            return $info;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions', 'info', 'leave_info'])->make(true);

        return $result;
    }

    public function create(Company $company)
    {
        return view('admin.company.employee.create', compact('company'));
    }

    public function store(Photoshop $photoshop, Company $company, Request $request)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone',
            'identity_type' => 'nullable|in:Malaysia IC,Passport Number',
            'identity_no' => 'nullable|required_if:identity_type,Malaysia IC,Passport Number',
            'employment_status' => 'required|in:FT,PT,Intern',
            'gender' => 'nullable|in:Male,Female,Other',
            'yearly_annual_leave_entitlement' => 'required|numeric',
            'yearly_medical_leave_entitlement' => 'required|numeric',
        ])->validate();

        if ($company->employees()->where('email', $request->email)->exists()) {
            Session::flash('alert-danger', 'Email already exists');

            return redirect()->back()->withInput();
        }

        $employee = new Employee;
        $employee->fill($request->all());
        $employee->company_id = $company->id;

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $employee->avatar = asset('storage/'.$filename);
        }

        $employee->password = env('APP_NAME').date('Y');
        $employee->save();

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.company.employee', [$company->id]);
    }

    public function edit(Company $company, Employee $employee)
    {
        return view('admin.company.employee.edit', compact('company', 'employee'));
    }

    public function update(Photoshop $photoshop, Request $request, Company $company, Employee $employee)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone',
            'identity_type' => 'nullable|in:Malaysia IC,Passport Number',
            'identity_no' => 'nullable|required_if:identity_type,Malaysia IC,Passport Number',
            'employment_status' => 'required|in:FT,PT,Intern',
            'gender' => 'nullable|in:Male,Female,Other',
            'yearly_annual_leave_entitlement' => 'required|numeric',
            'yearly_medical_leave_entitlement' => 'required|numeric',
        ])->validate();

        if ($company->employees()->where('email', $request->email)->where('id', '!=', $employee->id)->exists()) {
            Session::flash('alert-danger', 'Email already exists');

            return redirect()->back()->withInput();
        }

        $employee->fill($request->all());
        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $employee->avatar = asset('storage/'.$filename);
        }
        $employee->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.company.employee.edit', [$company->id, $employee->id]);
    }

    public function delete(Company $company, Employee $employee)
    {
        $employee->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.company.employee', [$company->id]);
    }
}
