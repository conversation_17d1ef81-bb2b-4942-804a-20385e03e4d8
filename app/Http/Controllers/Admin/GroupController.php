<?php

namespace App\Http\Controllers\Admin;

use App\Models\Group;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\DataTables;

class GroupController extends Controller
{
    public function index()
    {
        return view('admin.group.index');
    }

    public function query()
    {
        $query = Group::query()->with('translations');

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.group.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('translations.name', function ($row) {
            return getTranslation($row, 'name');
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->editColumn('is_active', function ($row) {
            return $row->is_active;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        $languages = Language::isActive()->get();

        return view('admin.group.create', compact('languages'));
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'nullable|unique:groups',
            'name.en' => 'required',
            'is_active' => 'boolean',
        ])->validate();

        $group = new Group;
        $group->fill($request->all());
        $group->save();

        foreach ($request->name as $key => $name) {
            $group->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.group');
    }

    public function edit(Group $group)
    {
        $languages = Language::isActive()->get();

        return view('admin.group.edit', compact('group', 'languages'));
    }

    public function update(Request $request, Group $group)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'name.en' => 'required',
            'slug' => 'nullable|unique:groups,slug,'.$group->id,
            'is_active' => 'boolean',
        ])->validate();

        $group->fill($request->all());
        $group->save();

        foreach ($request->name as $key => $name) {
            $group->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.group.edit', $group);
    }

    public function delete(Group $group)
    {
        $group->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.group');
    }
}
