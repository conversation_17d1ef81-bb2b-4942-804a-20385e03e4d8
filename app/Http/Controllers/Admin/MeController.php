<?php

namespace App\Http\Controllers\Admin;

use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class MeController extends Controller
{
    public function edit()
    {
        return view('admin.me.edit');
    }

    public function update(Photoshop $photoshop, Request $request)
    {
        $user = Auth::user();

        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:admins,email,'.$user->id,
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone|unique:admins,phone_e164,'.$user->id,
        ])->validate();

        $user->fill($request->all());

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $user->avatar = asset('storage/'.$filename);
        }

        $user->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.me.edit', $user);
    }

    public function editPassword()
    {
        return view('admin.me.edit-password');
    }

    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        Validator::make($request->all(), [
            'password' => 'current_password',
            'new_password' => ['required', 'confirmed', Password::min(8)],
        ])->validate();

        $user->password = $request->get('new_password');
        $user->save();

        Session::flash('alert-success', 'Successfully updated');

        return redirect()->route('admin.me.edit-password');
    }
}
