<?php

namespace App\Http\Controllers\Admin;

use App\Models\Blog;
use App\Models\Language;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\DataTables;

class BlogController extends Controller
{
    public function index()
    {
        return view('admin.blog.index');
    }

    public function query()
    {
        $query = Blog::query()->with('translations');

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.blog.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('translations.title', function ($row) {
            return getTranslation($row, 'title');
        })->editColumn('translations.description', function ($row) {
            return getTranslation($row, 'description');
        })->editColumn('published_at', function ($row) {
            return $row->published_at;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        $languages = Language::isActive()->get();

        return view('admin.blog.create', compact('languages'));
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'nullable|unique:blogs',
            'cover' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_landscape' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'author' => 'required',
            'content.en' => 'required',
            'published_at' => 'required|date',
        ])->validate();

        $blog = new Blog;
        $blog->fill($request->all());

        $filename = $request->file('cover')->store('images', 'public');
        $blog->cover = asset('storage/'.$filename);

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $blog->cover_landscape = asset('storage/'.$filename);
        }

        $blog->save();

        foreach ($request->title as $key => $title) {
            $blog->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.blog');
    }

    public function edit(Blog $blog)
    {
        $languages = Language::isActive()->get();

        return view('admin.blog.edit', compact('blog', 'languages'));
    }

    public function update(Request $request, Blog $blog)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'slug' => 'nullable|unique:blogs,slug,'.$blog->id,
            'author' => 'required',
            'published_at' => 'required|date',
            'content.en' => 'required',
        ])->validate();

        $request->merge([
            'published_at' => Carbon::parse($request->get('published_at'))->format('Y-m-d H:i:s'),
        ]);

        $blog->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $blog->cover = asset('storage/'.$filename);
        }

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $blog->cover_landscape = asset('storage/'.$filename);
        }

        $blog->save();

        foreach ($request->title as $key => $title) {
            $blog->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.blog.edit', $blog);
    }

    public function delete(Blog $blog)
    {
        $blog->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.blog');
    }
}
