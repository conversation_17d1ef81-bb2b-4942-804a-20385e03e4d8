<?php

namespace App\Http\Controllers\Admin;

use App\Models\Booking;
use App\Services\GoogleCalendar;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;

class BookingController extends Controller
{
    public function index()
    {
        return view('admin.booking.index');
    }

    public function query()
    {
        $query = Booking::query()->with(['user', 'service.translations']);

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.booking.show', [$row->id]).'" class="btn btn-sm btn-info mx-1 mb-1"> Show</a>';
            $actions .= '<a href="'.route('admin.booking.invoice', [$row->id]).'" class="btn btn-sm btn-warning mx-1 mb-1"> Invoice</a>';

            return $actions;
        })->editColumn('tax', function ($row) {
            return $row->tax > 0 ? ($row->tax_name.' RM '.$row->tax) : 0;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->editColumn('service.translations.name', function ($row) {
            return getTranslation($row->service, 'name');
        })->addColumn('paid_at', function ($row) {
            return $row->paid_at ?? '-';
        })->addColumn('failed_at', function ($row) {
            return $row->failed_at ?? '-';
        })->addColumn('cancelled_at', function ($row) {
            return $row->cancelled_at ?? '-';
        })->addColumn('accepted_at', function ($row) {
            return $row->accepted_at ?? '-';
        })->addColumn('completed_at', function ($row) {
            return $row->completed_at ?? '-';
        })->addColumn('user_info', function ($row) {
            if ($row->user) {
                $info = '<a href='.route('admin.user.show', [$row->user->id])." target='_blank'> <strong> Name: </strong>".$row->name.' <br/> <strong> Email: </strong>'.$row->email.' <br/> <strong> Phone: </strong>'.$row->phone_e164.'</a>';
            } else {
                $info = '<strong> Name: </strong>'.$row->name.' <br/> <strong> Email: </strong>'.$row->email.' <br/> <strong> Phone: </strong>'.$row->phone_e164;
            }

            return $info;
        })->addColumn('service_info', function ($row) {
            $info = '<strong> Name: </strong>'.collect($row->service_meta->translations)->firstWhere('language_code', 'en')->name.' <br/> <strong> Date: </strong>'.$row->date->format('Y-m-d').' <br/> <strong> Time: </strong>'.$row->time;

            return $info;
        })->rawColumns(['actions', 'user_info', 'service_info'])->make(true);

        return $result;
    }

    public function show(Booking $booking)
    {
        $booking->load('payments');

        return view('admin.booking.show', compact('booking'));
    }

    public function update(Booking $booking, Request $request)
    {
        Validator::make($request->all(), [
            'status' => 'required|in:Cancelled,Accepted,Completed',
        ])->validate();
        switch ($request->status) {
            case 'Cancelled':
                if ($booking->cancelled_at) {
                    Session::flash('alert-danger', 'booking already cancelled');

                    return redirect()->route('admin.booking.show', [$booking->id]);
                }
                $booking->cancelled_at = now();

                $timeSlot = $booking->timeSlot;
                $timeSlot->is_booked = false;
                $timeSlot->save();

                $googleCalendar = new GoogleCalendar;
                $googleCalendar = $googleCalendar->deleteEvent($booking);
                break;
            case 'Accepted':
                if ($booking->accepted_at || $booking->completed_at || $booking->cancelled_at) {
                    Session::flash('alert-danger', 'Cannot accept booking, already completed or cancelled');

                    return redirect()->route('admin.booking.show', [$booking->id]);
                }
                $booking->google_meet_link = $request->google_meet_link;
                $booking->internal_note = $request->internal_note;
                $booking->accepted_at = now();

                $googleCalendar = new GoogleCalendar;
                $googleCalendar = $googleCalendar->createEvent($booking);
                break;
            case 'Completed':
                if ($booking->completed_at || $booking->cancelled_at || ! $booking->accepted_at) {
                    Session::flash('alert-danger', 'Cannot complete booking, already completed or cancelled');

                    return redirect()->route('admin.booking.show', [$booking->id]);
                }
                $booking->completed_at = now();
                break;
        }

        $booking->save();

        Notification::route('mail', $booking->email)->notify(new \App\Notifications\User\Booking\UpdateStatus($booking));
        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.booking.show', [$booking->id]);
    }

    public function invoice(Booking $booking)
    {
        $html = view('pdf.booking-invoice', compact('booking'))->render();

        return Pdf::loadHTML($html)->setPaper('a4', 'landscape')->setWarnings(false)->download('invoice-'.$booking->no.'.pdf');
    }

    public function downloadReceipt(Booking $booking, $payment)
    {
        $payment = $booking->payments()->findOrFail($payment);

        if (! $payment->receipt_path || ! Storage::exists($payment->receipt_path)) {
            abort(404, 'Receipt not found');
        }

        return Storage::download(
            $payment->receipt_path,
            $payment->receipt_original_filename ?? 'receipt.pdf'
        );
    }

    public function previewReceipt(Booking $booking, $payment)
    {
        $payment = $booking->payments()->findOrFail($payment);

        if (! $payment->receipt_path || ! Storage::exists($payment->receipt_path)) {
            abort(404, 'Receipt not found');
        }

        $file = Storage::get($payment->receipt_path);
        $mimeType = $payment->receipt_mime_type ?? 'application/octet-stream';
        $filename = $payment->receipt_original_filename ?? 'receipt.pdf';

        return response($file, 200)
            ->header('Content-Type', $mimeType)
            ->header('Content-Disposition', 'inline; filename="'.$filename.'"');
    }
}
