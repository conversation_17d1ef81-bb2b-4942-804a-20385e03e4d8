<?php

namespace App\Http\Controllers\Admin;

use App\Models\Language;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\DataTables;

class ServiceController extends Controller
{
    public function index()
    {
        return view('admin.service.index');
    }

    public function query()
    {
        $query = Service::query()->with(['company', 'category.translations', 'translations']);

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.service.edit', [$row->id]).'" class="btn btn-sm btn-success mx-1"> Edit</a>';

            return $actions;
        })->editColumn('translations.name', function ($row) {
            return getTranslation($row, 'name');
        })->editColumn('translations.description', function ($row) {
            return getTranslation($row, 'description');
        })->editColumn('category.translations.name', function ($row) {
            return getTranslation($row->category, 'name');
        })->editColumn('group.translations.name', function ($row) {
            return getTranslation($row->group, 'name');
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        $languages = Language::isActive()->get();

        return view('admin.service.create', compact('languages'));
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'required|unique:services',
            'cover' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'name.en' => 'required',
            'group_id' => 'required|exists:groups,id',
            'description.en' => 'nullable|max:255',
            'company_id' => 'required|exists:companies,id',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric',
            'content.en' => 'required',
        ])->validate();

        $service = new Service;
        $service->fill($request->all());
        $filename = $request->file('cover')->store('images', 'public');
        $service->cover = asset('storage/'.$filename);
        $service->save();

        foreach ($request->name as $key => $name) {
            $service->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.service');
    }

    public function edit(Service $service)
    {
        $languages = Language::isActive()->get();

        return view('admin.service.edit', compact('service', 'languages'));
    }

    public function update(Request $request, Service $service)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'required|unique:services,slug,'.$service->id,
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'name.en' => 'required',
            'group_id' => 'required|exists:groups,id',
            'description.en' => 'nullable|max:255',
            'company_id' => 'required|exists:companies,id',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric',
            'content.en' => 'required',
        ])->validate();

        $service->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $service->cover = asset('storage/'.$filename);
        }

        $service->save();

        foreach ($request->name as $key => $name) {
            $service->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.service.edit', $service);
    }

    public function delete(Service $service)
    {
        $service->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.service');
    }
}
