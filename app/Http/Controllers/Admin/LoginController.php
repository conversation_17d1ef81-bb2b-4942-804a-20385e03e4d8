<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    public function index()
    {
        return view('admin.login');
    }

    public function authenticate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => 'Please fill in required fields']);
        } else {
            $credentials = $request->only('email', 'password');
            if (Auth::attempt($credentials, $request->get('remember'))) {
                return redirect()->intended(route('admin'));
            } else {
                return redirect()->back()->withInput()->withErrors(['auth' => 'Incorrect Email or Password']);
            }
        }
    }

    public function logout()
    {
        Auth::logout();

        return redirect()->route('admin.login');
    }
}
