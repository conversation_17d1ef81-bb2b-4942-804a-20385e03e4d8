<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Str;
use Yajra\DataTables\DataTables;

class CompanyController extends Controller
{
    public function index()
    {
        return view('admin.company.index');
    }

    public function query()
    {
        $query = Company::query();

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.company.edit', [$row->id]).'" class="btn btn-sm btn-success mx-1"> Edit</a>';
            $actions .= '<a href="'.route('admin.company.show', [$row->id]).'" class="btn btn-sm btn-info mx-1"> Show</a>';

            return $actions;
        })->addColumn('main_pic_info', function ($row) {
            $info = '<strong> Name: </strong>'.$row->main_pic_name.'<br>';
            $info .= '<strong> Phone: </strong>'.$row->main_pic_phone_e164.'<br>';
            $info .= '<strong> Email: </strong>'.$row->main_pic_email.'<br>';
            $info .= '<strong> Address: </strong>'.$row->main_pic_address;

            return $info;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions', 'main_pic_info'])->make(true);

        return $result;
    }

    public function create()
    {
        return view('admin.company.create');
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')),
        ]);

        Validator::make($request->all(), [
            'name' => 'required',
            'registration_no' => 'required|unique:companies,registration_no',
            'legal_name' => 'required',
        ])->validate();

        $company = new Company;
        $company->slug = $request->get('slug');
        $company->fill($request->all());

        if ($request->hasFile('logo')) {
            $filename = $request->file('logo')->store('images', 'public');
            $company->logo = asset('storage/'.$filename);
        }

        $company->save();
        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.company');
    }

    public function edit(Company $company)
    {
        return view('admin.company.edit', compact('company'));
    }

    public function update(Request $request, Company $company)
    {

        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')),
        ]);

        Validator::make($request->all(), [
            'name' => 'required',
            'registration_no' => 'required|unique:companies,registration_no,'.$company->id,
            'legal_name' => 'required',
        ])->validate();

        $company->slug = $request->get('slug');
        $company->fill($request->all());

        if ($request->hasFile('logo')) {
            $filename = $request->file('logo')->store('images', 'public');
            $company->logo = asset('storage/'.$filename);
        }
        $company->save();
        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.company.edit', $company);
    }

    public function show(Company $company)
    {
        return view('admin.company.show', compact('company'));
    }

    public function delete(Company $company)
    {
        $company->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.company');
    }
}
