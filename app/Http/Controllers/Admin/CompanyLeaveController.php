<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\LeaveAttachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class CompanyLeaveController extends Controller
{
    public function index(Company $company)
    {
        return view('admin.company.leave.index', compact('company'));
    }

    public function query(Company $company)
    {
        $leaves = Leave::with('employee')
            ->join('employees', 'leaves.employee_id', '=', 'employees.id')
            ->where('leaves.company_id', $company->id)
            ->select([
                'leaves.*',
                'employees.name as employee_name',
                'employees.email as employee_email',
            ]);

        return DataTables::of($leaves)
            ->addColumn('actions', function ($row) use ($company) {
                $actions = '<div class="d-flex justify-content-end flex-shrink-0">';

                $actions .= '<a href="'.route('admin.company.leave.show', [$company, $row]).'" class="btn btn-sm btn-info mx-1">Show</a>';

                if (! $row->approved_at && ! $row->rejected_at) {
                    $actions .= '<a href="'.route('admin.company.leave.approve', [$company, $row]).'" class="btn btn-sm btn-success mx-1" onclick="return confirm(\'Are you sure you want to approve this leave?\');">Approve</a>';

                    $actions .= '<a href="'.route('admin.company.leave.reject', [$company, $row]).'" class="btn btn-sm btn-danger mx-1">Reject</a>';
                }

                $actions .= '</div>';

                return $actions;
            })
            ->addColumn('employee_info', function ($row) {
                $info = '<strong>Name:</strong> '.$row->employee_name.'<br>';
                $info .= '<strong>Email:</strong> '.$row->employee_email.'<br>';
                $info .= '<strong>Phone:</strong> '.($row->employee->phone_e164 ?? 'N/A');

                return $info;
            })
            ->editColumn('type', function ($row) {
                $types = [
                    'AL' => 'Annual Leave',
                    'MC' => 'Medical Leave',
                    'NPL' => 'No Pay Leave',
                ];

                return $types[$row->type] ?? $row->type;
            })
            ->editColumn('start_date', function ($row) {
                return $row->start_date;
            })
            ->editColumn('end_date', function ($row) {
                return $row->end_date;
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at;
            })
            ->editColumn('status', function ($row) {
                if ($row->approved_at) {
                    return '<span class="badge badge-light-success">Approved</span>';
                } elseif ($row->rejected_at) {
                    return '<span class="badge badge-light-danger">Rejected</span>';
                } else {
                    return '<span class="badge badge-light-warning">Pending</span>';
                }
            })
            ->filterColumn('employee_info', function ($query, $keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('employees.name', 'like', "%{$keyword}%")
                        ->orWhere('employees.email', 'like', "%{$keyword}%");
                });
            })
            ->rawColumns(['actions', 'employee_info', 'status'])
            ->make(true);
    }

    public function show(Company $company, Leave $leave)
    {
        if ($leave->company_id !== $company->id) {
            abort(404);
        }

        return view('admin.company.leave.show', compact('company', 'leave'));
    }

    public function approve(Company $company, Leave $leave)
    {
        if ($leave->company_id !== $company->id || $leave->approved_at || $leave->rejected_at) {
            abort(404);
        }

        $employee = $leave->employee;

        if ($leave->type === 'AL') {
            $availableBalance = max(0, $employee->yearly_annual_leave_entitlement - $employee->taken_annual_leave);

            if ($availableBalance < $leave->duration) {
                Session::flash('alert-danger', "Cannot approve leave. Employee doesn't have enough annual leave balance. Available: {$availableBalance} days, Requested: {$leave->duration} days");

                return redirect()->back();
            }

            $newTakenLeave = $employee->taken_annual_leave + $leave->duration;
            Employee::where('id', $employee->id)->update(['taken_annual_leave' => $newTakenLeave]);

        } elseif ($leave->type === 'MC') {
            $availableBalance = max(0, $employee->yearly_medical_leave_entitlement - $employee->taken_medical_leave);

            if ($availableBalance < $leave->duration) {
                Session::flash('alert-danger', "Cannot approve leave. Employee doesn't have enough medical leave balance. Available: {$availableBalance} days, Requested: {$leave->duration} days");

                return redirect()->back();
            }

            $newTakenLeave = $employee->taken_medical_leave + $leave->duration;
            Employee::where('id', $employee->id)->update(['taken_medical_leave' => $newTakenLeave]);
        }

        $leave->approved_at = now();
        $leave->approver_type = 'App\Models\Admin';
        $leave->approver_id = Auth::guard('admin')->id();
        $leave->save();

        Notification::route('mail', $leave->employee->email)->notify(new \App\Notifications\Employee\Leave\Approved($leave));

        Session::flash('alert-success', 'Leave application approved successfully');

        return redirect()->route('admin.company.leave', $company);
    }

    public function rejectForm(Company $company, Leave $leave)
    {
        if ($leave->company_id !== $company->id || $leave->approved_at || $leave->rejected_at) {
            abort(404);
        }

        return view('admin.company.leave.reject', compact('company', 'leave'));
    }

    public function reject(Company $company, Leave $leave, Request $request)
    {
        if ($leave->company_id !== $company->id || $leave->approved_at || $leave->rejected_at) {
            abort(404);
        }

        $validator = Validator::make($request->all(), [
            'reject_reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator);
        }

        $leave->rejected_at = now();
        $leave->rejecter_type = 'App\Models\Admin';
        $leave->rejecter_id = Auth::guard('admin')->id();
        $leave->reject_reason = $request->reject_reason;
        $leave->save();

        Notification::route('mail', $leave->employee->email)->notify(new \App\Notifications\Employee\Leave\Rejected($leave));

        Session::flash('alert-success', 'Leave application rejected successfully');

        return redirect()->route('admin.company.leave', $company);
    }

    public function downloadAttachment(Company $company, Leave $leave, LeaveAttachment $attachment)
    {
        if ($leave->company_id !== $company->id) {
            abort(404);
        }

        if ($attachment->leave_id !== $leave->id) {
            abort(404, 'Attachment not found.');
        }

        return Storage::download($attachment->file_path, $attachment->original_filename);
    }

    public function previewAttachment(Company $company, Leave $leave, LeaveAttachment $attachment)
    {
        if ($leave->company_id !== $company->id) {
            abort(404);
        }

        if ($attachment->leave_id !== $leave->id) {
            abort(404, 'Attachment not found.');
        }

        if (! Storage::exists($attachment->file_path)) {
            abort(404, 'File not found.');
        }

        $file = Storage::get($attachment->file_path);

        return response($file, 200)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Disposition', 'inline; filename="'.$attachment->original_filename.'"');
    }
}
