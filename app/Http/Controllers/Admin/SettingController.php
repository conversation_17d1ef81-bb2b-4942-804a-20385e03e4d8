<?php

namespace App\Http\Controllers\Admin;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SettingController extends Controller
{
    public function index()
    {
        return view('admin.setting.index');
    }

    public function paymentMethods()
    {
        $paymentMethods = Setting::get('payment_methods', []);

        return view('admin.setting.payment-methods', compact('paymentMethods'));
    }

    public function updatePaymentMethods(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'bank_name' => 'required|string|max:255',
            'bank_account_number' => 'required|string|max:255',
            'bank_account_name' => 'required|string|max:255',
            'qr_code_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $paymentMethods = Setting::get('payment_methods', []);

        $paymentMethods['bank_name'] = $request->bank_name;
        $paymentMethods['bank_account_number'] = $request->bank_account_number;
        $paymentMethods['bank_account_name'] = $request->bank_account_name;

        if ($request->hasFile('qr_code_image')) {
            if (isset($paymentMethods['qr_code_image_path']) && Storage::exists($paymentMethods['qr_code_image_path'])) {
                Storage::delete($paymentMethods['qr_code_image_path']);
            }

            $filename = Str::ulid().'.'.$request->file('qr_code_image')->getClientOriginalExtension();
            $path = $request->file('qr_code_image')->storeAs('payment_methods', $filename);

            $paymentMethods['qr_code_image_path'] = $path;
            $paymentMethods['qr_code_original_filename'] = $request->file('qr_code_image')->getClientOriginalName();
            $paymentMethods['qr_code_mime_type'] = $request->file('qr_code_image')->getMimeType();
        }

        Setting::set('payment_methods', $paymentMethods, 'payment');

        Session::flash('alert-success', 'Payment methods updated successfully');

        return redirect()->route('admin.setting.payment-methods');
    }

    public function serveQrCodeImage()
    {
        $paymentMethods = Setting::get('payment_methods', []);

        if (! isset($paymentMethods['qr_code_image_path']) || ! Storage::exists($paymentMethods['qr_code_image_path'])) {
            abort(404, 'QR code image not found');
        }

        $file = Storage::get($paymentMethods['qr_code_image_path']);
        $mimeType = $paymentMethods['qr_code_mime_type'] ?? 'image/png';
        $filename = $paymentMethods['qr_code_original_filename'] ?? 'qr-code.png';

        return response($file, 200)
            ->header('Content-Type', $mimeType)
            ->header('Content-Disposition', 'inline; filename="'.$filename.'"');
    }
}
