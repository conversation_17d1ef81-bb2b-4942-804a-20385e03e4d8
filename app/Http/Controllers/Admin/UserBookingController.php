<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Yajra\DataTables\DataTables;

class UserBookingController extends Controller
{
    public function index(User $user)
    {
        return view('admin.user.booking.index', compact('user'));
    }

    public function query(User $user)
    {
        $query = $user->bookings();

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.booking.show', [$row->id]).'" class="btn btn-sm btn-info mx-1 mb-1" target="_blank">View</a>';

            return $actions;
        })->editColumn('tax', function ($row) {
            return $row->tax_name.' RM '.$row->tax;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->addColumn('status', function ($row) {
            if ($row->status == 'Failed') {
                return '<span class="badge badge-light-danger">Failed At '.$row->failed_at.'</span>';
            } elseif ($row->status == 'Cancelled') {
                return '<span class="badge badge-light-danger">Cancelled At '.$row->cancelled_at.'</span>';
            } elseif ($row->status == 'Completed') {
                return '<span class="badge badge-light-success">Completed At '.$row->completed_at.'</span>';
            } elseif ($row->status == 'Accepted') {
                return '<span class="badge badge-light-primary">Accepted At '.$row->accepted_at.'</span>';
            } elseif ($row->status == 'Paid') {
                return '<span class="badge badge-light-success">Paid At '.$row->paid_at.'</span>';
            } else {
                return '<span class="badge badge-light-warning">Pending</span>';
            }
        })->addColumn('service_info', function ($row) {
            $info = '<strong> Name: </strong>'.$row->service->name.' <br/> <strong> Date: </strong>'.$row->date->format('Y-m-d').' <br/> <strong> Time: </strong>'.$row->time;

            return $info;
        })->rawColumns(['actions', 'status', 'service_info'])->make(true);

        return $result;
    }
}
