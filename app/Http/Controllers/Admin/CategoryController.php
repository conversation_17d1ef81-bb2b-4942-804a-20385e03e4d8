<?php

namespace App\Http\Controllers\Admin;

use App\Models\Category;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\DataTables;

class CategoryController extends Controller
{
    public function index()
    {
        return view('admin.category.index');
    }

    public function query()
    {
        $query = Category::query()->with('translations');

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.category.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('translations.name', function ($row) {
            return getTranslation($row, 'name');
        })->editColumn('translations.description', function ($row) {
            return getTranslation($row, 'description');
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        $languages = Language::isActive()->get();

        return view('admin.category.create', compact('languages'));
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'nullable|unique:categories',
            'cover' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'name.en' => 'required',
            'description.en' => 'required',
            'section' => 'required|in:service',
            'sequence' => 'required|integer',
        ])->validate();

        $category = new Category;
        $category->fill($request->all());

        $filename = $request->file('cover')->store('images', 'public');
        $category->cover = asset('storage/'.$filename);
        $category->save();

        foreach ($request->name as $key => $name) {
            $category->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                    'description' => $request->description[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.category');
    }

    public function edit(Category $category)
    {
        $languages = Language::isActive()->get();

        return view('admin.category.edit', compact('category', 'languages'));
    }

    public function update(Request $request, Category $category)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('name')['en']),
        ]);

        Validator::make($request->all(), [
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'name.en' => 'required',
            'slug' => 'nullable|unique:categories,slug,'.$category->id,
            'description.en' => 'required',
            'section' => 'required|in:service',
            'sequence' => 'required|integer',
        ])->validate();

        $category->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $category->cover = asset('storage/'.$filename);
        }

        $category->save();

        foreach ($request->name as $key => $name) {
            $category->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'name' => $name,
                    'description' => $request->description[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.category.edit', $category);
    }

    public function delete(Category $category)
    {
        $category->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.category');
    }
}
