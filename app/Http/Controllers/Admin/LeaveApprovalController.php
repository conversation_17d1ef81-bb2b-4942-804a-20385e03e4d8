<?php

namespace App\Http\Controllers\Admin;

use App\Models\Leave;
use Yajra\DataTables\Facades\DataTables;

class LeaveApprovalController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // The pending count is now handled in the AppServiceProvider
    }

    /**
     * Display a listing of pending leave approvals.
     */
    public function index()
    {
        return view('admin.leave-approval.index');
    }

    /**
     * Get pending leaves for DataTables.
     */
    public function query()
    {
        $leaves = Leave::with(['employee', 'company'])
            ->whereNull('approved_at')
            ->whereNull('rejected_at')
            ->select('*');

        return DataTables::of($leaves)
            ->addColumn('actions', function ($row) {
                $actions = '<div class="d-flex justify-content-end flex-shrink-0">';

                $actions .= '<a href="'.route('admin.company.leave.show', [$row->company, $row]).'" class="btn btn-sm btn-info mx-1" target="_blank">Show</a>';
                $actions .= '<a href="'.route('admin.company.leave.approve', [$row->company, $row]).'" class="btn btn-sm btn-success mx-1" target="_blank" onclick="return confirm(\'Are you sure you want to approve this leave?\');">Approve</a>';
                $actions .= '<a href="'.route('admin.company.leave.reject', [$row->company, $row]).'" class="btn btn-sm btn-danger mx-1" target="_blank">Reject</a>';

                $actions .= '</div>';

                return $actions;
            })
            ->addColumn('employee_info', function ($row) {
                $info = '<strong>Name:</strong> '.$row->employee->name.'<br>';
                $info .= '<strong>Email:</strong> '.$row->employee->email;

                return $info;
            })
            ->addColumn('company_name', function ($row) {
                return $row->company->name;
            })
            ->editColumn('type', function ($row) {
                $types = [
                    'AL' => 'Annual Leave',
                    'MC' => 'Medical Leave',
                    'NPL' => 'No Pay Leave',
                ];

                return $types[$row->type] ?? $row->type;
            })
            ->editColumn('start_date', function ($row) {
                return $row->start_date;
            })
            ->editColumn('end_date', function ($row) {
                return $row->end_date;
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at;
            })
            ->rawColumns(['actions', 'employee_info', 'company_name'])
            ->make(true);
    }
}
