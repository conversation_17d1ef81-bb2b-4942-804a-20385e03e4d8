<?php

namespace App\Http\Controllers\Admin;

use App\Models\Highlight;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\DataTables;

class HighlightController extends Controller
{
    public function index()
    {
        return view('admin.highlight.index');
    }

    public function query()
    {
        $query = Highlight::query()->with('translations');

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.highlight.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('translations.title', function ($row) {
            return getTranslation($row, 'title');
        })->editColumn('translations.description', function ($row) {
            return getTranslation($row, 'description');
        })->editColumn('start_at', function ($row) {
            return $row->start_at;
        })->editColumn('end_at', function ($row) {
            return $row->end_at;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        $languages = Language::isActive()->get();

        return view('admin.highlight.create', compact('languages'));
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'nullable|unique:highlights',
            'cover' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_landscape' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'description.en' => 'nullable|max:255',
            'button_text.en' => 'nullable',
            'content.en' => 'nullable',
            'external_url' => 'nullable',
            'sequence' => 'required|integer',
            'start_at' => 'nullable|date',
            'end_at' => 'nullable|date',
        ])->validate();

        $highlight = new Highlight;
        $highlight->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $highlight->cover = asset('storage/'.$filename);
        }

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $highlight->cover_landscape = asset('storage/'.$filename);
        }

        $highlight->save();

        foreach ($request->title as $key => $title) {
            $highlight->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'button_text' => $request->button_text[$key] ?? null,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.highlight');
    }

    public function edit(Highlight $highlight)
    {
        $languages = Language::isActive()->get();

        return view('admin.highlight.edit', compact('highlight', 'languages'));
    }

    public function update(Request $request, Highlight $highlight)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_landscape' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'slug' => 'nullable|unique:highlights,slug,'.$highlight->id,
            'description.en' => 'nullable|max:255',
            'button_text.en' => 'nullable',
            'content.en' => 'nullable',
            'sequence' => 'required|integer',
            'start_at' => 'nullable|date',
            'end_at' => 'nullable|date',
        ])->validate();

        $highlight->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $highlight->cover = asset('storage/'.$filename);
        }

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $highlight->cover_landscape = asset('storage/'.$filename);
        }

        $highlight->save();

        foreach ($request->title as $key => $title) {
            $highlight->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'button_text' => $request->button_text[$key] ?? null,
                    'description' => $request->description[$key] ?? null,
                    'content' => $request->content[$key] ?? null,
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.highlight.edit', $highlight);
    }

    public function delete(Highlight $highlight)
    {
        $highlight->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.highlight');
    }
}
