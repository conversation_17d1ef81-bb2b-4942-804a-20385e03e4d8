<?php

namespace App\Http\Controllers\Admin;

use App\Models\Booking;
use App\Models\Leave;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CalendarController extends Controller
{
    public function index()
    {
        return view('admin.calendar');
    }

    public function event(Request $request)
    {
        // Get booking events
        $bookingEvents = Booking::whereNotNull('accepted_at')->whereNull('failed_at')->whereNull('cancelled_at')->whereNotNull('paid_at')
            ->with(['user', 'service', 'timeSlot'])->get()
            ->map(function ($booking) {
                // time = 10:00AM - 11:00AM
                $time = explode(' - ', $booking->timeSlot->time);

                return [
                    'title' => $booking->name.' - '.$booking->service_meta->name,
                    'start' => $booking->date->format('Y-m-d').'T'.Carbon::createFromFormat('h:i A', $time[0])->format('H:i'),
                    'end' => $booking->date->format('Y-m-d').'T'.Carbon::createFromFormat('h:i A', $time[1])->format('H:i'),
                    'url' => route('admin.booking.show', $booking),
                    'backgroundColor' => '#3699FF', // Blue color for bookings
                    'borderColor' => '#3699FF',
                ];
            });

        // Get approved leave events
        $leaveEvents = Leave::with(['employee', 'company'])
            ->whereNotNull('approved_at')
            ->get()
            ->map(function ($leave) {
                $leaveType = '';
                $backgroundColor = '';

                if ($leave->type === 'AL') {
                    $leaveType = 'Annual Leave';
                    $backgroundColor = '#20c997'; // Green
                } elseif ($leave->type === 'MC') {
                    $leaveType = 'Medical Leave';
                    $backgroundColor = '#F64E60'; // Red
                } else {
                    $leaveType = 'No Pay Leave';
                    $backgroundColor = '#FFA800'; // Yellow
                }

                return [
                    'id' => $leave->id,
                    'title' => $leave->company->name.'|'.$leave->employee->name.' - '.$leaveType.($leave->remark ? '  With Remark : '.$leave->remark : ''),
                    'start' => $leave->start_date->format('Y-m-d'),
                    'end' => $leave->end_date->addDay()->format('Y-m-d'), // Add a day to make it inclusive
                    'url' => route('admin.company.leave.show', [$leave->company, $leave]),
                    'backgroundColor' => $backgroundColor,
                    'borderColor' => $backgroundColor,
                    'textColor' => '#ffffff',
                    'allDay' => true,
                ];
            });

        // Combine both event types
        $events = $bookingEvents->concat($leaveEvents);

        return response()->json($events);
    }
}
