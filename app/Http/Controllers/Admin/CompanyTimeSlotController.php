<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\DataTables;

class CompanyTimeSlotController extends Controller
{
    public function index(Company $company)
    {
        return view('admin.company.time-slot.index', compact('company'));
    }

    public function query(Company $company, Request $request)
    {
        $query = $company->timeSlots()->when($request->get('month'), function ($query) use ($request) {
            $query->whereMonth('date', Carbon::parse($request->get('month'))->format('m'))
                ->whereYear('date', Carbon::parse($request->get('month'))->format('Y'));
        })->orderBy('date', 'asc');

        $result = DataTables::of($query)->editColumn('date', function ($row) {
            return $row->date->format('Y-m-d');
        })->make(true);

        return $result;
    }

    public function export(Company $company, Request $request)
    {
        Validator::make($request->all(), [
            'month' => 'required|date_format:Y-m',
        ])->validate();

        $date = Carbon::createFromFormat('Y-m', $request->get('month'));

        return Excel::download(new \App\Exports\Company\TimeSlot\All($date, $company), now()->format('YmdHis').'_'.$company->name.'_time_slots.xlsx');
    }

    public function import(Company $company, Request $request)
    {
        Validator::make($request->all(), [
            'month' => 'required|date_format:Y-m',
            'excel' => 'required|mimes:xlsx,xls',
        ])->validate();

        try {
            $date = Carbon::createFromFormat('Y-m', $request->get('month'));
            $file = $request->file('excel');

            Excel::import(new \App\Imports\Company\TimeSlot\UpdateOrCreate($date, $company), $file);
            Session::flash('alert-success', 'Successfully Imported');

            return redirect()->route('admin.company.time-slot', $company);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return back()->withErrors(['excel' => $e->errors() ? $e->errors()[0] : $e->getMessage()])->withInput();
        }
    }
}
