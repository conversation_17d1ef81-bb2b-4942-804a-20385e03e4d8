<?php

namespace App\Http\Controllers\Admin;

use App\Models\Admin;
use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Ya<PERSON>ra\DataTables\DataTables;

class AdminController extends Controller
{
    public function index()
    {
        return view('admin.admin.index');
    }

    public function query()
    {
        $query = Admin::query();

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.admin.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        return view('admin.admin.create');
    }

    public function store(Photoshop $photoshop, Request $request)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:admins',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone|unique:admins,phone_e164,',
        ])->validate();

        $admin = new Admin;
        $admin->fill($request->all());

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $admin->avatar = asset('storage/'.$filename);
        }

        $admin->password = env('APP_NAME').date('Y');
        $admin->save();

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.admin');
    }

    public function edit(Admin $admin)
    {
        return view('admin.admin.edit', compact('admin'));
    }

    public function update(Photoshop $photoshop, Request $request, Admin $admin)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:admins,email,'.$admin->id,
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'phone_e164' => 'nullable|phone|unique:admins,phone_e164,'.$admin->id,
        ])->validate();

        $admin->fill($request->all());

        if ($request->hasFile('avatar')) {
            $filename = $request->file('avatar')->hashName('avatars');
            $resized = $photoshop->take($request->file('avatar'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $admin->avatar = asset('storage/'.$filename);
        }

        $admin->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.admin.edit', $admin);
    }

    public function delete(Admin $admin)
    {
        $admin->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.admin');
    }
}
