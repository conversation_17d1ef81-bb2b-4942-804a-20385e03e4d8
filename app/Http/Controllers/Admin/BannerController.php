<?php

namespace App\Http\Controllers\Admin;

use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Str;
use Yajra\DataTables\DataTables;

class BannerController extends Controller
{
    public function index()
    {
        return view('admin.banner.index');
    }

    public function query()
    {
        $query = Banner::query()->with('translations');

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('admin.banner.edit', [$row->id]).'" class="btn btn-sm btn-success"> Edit</a>';

            return $actions;
        })->editColumn('translations.title', function ($row) {
            return getTranslation($row, 'title');
        })->editColumn('translations.description', function ($row) {
            return getTranslation($row, 'description');
        })->editColumn('start_at', function ($row) {
            return $row->start_at;
        })->editColumn('end_at', function ($row) {
            return $row->end_at;
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->rawColumns(['actions'])->make(true);

        return $result;
    }

    public function create()
    {
        return view('admin.banner.create');
    }

    public function store(Request $request)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'slug' => 'nullable|unique:banners',
            'cover' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_landscape' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'sequence' => 'required|integer',
            'start_at' => 'nullable|date',
            'end_at' => 'nullable|date',
        ])->validate();

        $banner = new Banner;
        $banner->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $banner->cover = asset('storage/'.$filename);
        }

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $banner->cover_landscape = asset('storage/'.$filename);
        }

        $banner->save();

        foreach ($request->title as $key => $title) {
            $banner->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'button_text' => $request->button_text[$key],
                    'description' => $request->description[$key],
                    'content' => $request->content[$key],
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.banner');
    }

    public function edit(Banner $banner)
    {
        return view('admin.banner.edit', compact('banner'));
    }

    public function update(Request $request, Banner $banner)
    {
        $request->merge([
            'slug' => Str::slug($request->get('slug') ?? $request->get('title')['en']),
        ]);

        Validator::make($request->all(), [
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_landsacpe' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'title.en' => 'required',
            'slug' => 'nullable|unique:banners,slug,'.$banner->id,
            'sequence' => 'required|integer',
            'start_at' => 'nullable|date',
            'end_at' => 'nullable|date',
        ])->validate();

        $banner->fill($request->all());

        if ($request->hasFile('cover')) {
            $filename = $request->file('cover')->store('images', 'public');
            $banner->cover = asset('storage/'.$filename);
        }

        if ($request->hasFile('cover_landscape')) {
            $filename = $request->file('cover_landscape')->store('images', 'public');
            $banner->cover_landscape = asset('storage/'.$filename);
        }

        $banner->save();

        foreach ($request->title as $key => $title) {
            $banner->translations()->updateOrCreate(
                ['language_code' => $key],
                [
                    'title' => $title,
                    'button_text' => $request->button_text[$key],
                    'description' => $request->description[$key],
                    'content' => $request->content[$key],
                ]
            );
        }

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.banner.edit', $banner);
    }

    public function delete(Banner $banner)
    {
        $banner->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.banner');
    }
}
