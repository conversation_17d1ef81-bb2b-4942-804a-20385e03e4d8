<?php

namespace App\Http\Controllers\Public;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class MeController extends Controller
{
    public function edit()
    {
        return view('public.me.edit');
    }

    public function update(Request $request)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:users,email,'.Auth::user()->id,
            'phone_e164' => 'nullable|phone|unique:users,phone_e164,'.Auth::user()->id,
        ])->validate();

        $user = Auth::user();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone_e164 = $request->phone_e164;
        $user->save();

        Session::flash('alert-success', 'Profile updated successfully');

        return redirect()->route('me.edit');
    }

    public function changePassword()
    {
        return view('public.me.change-password');
    }

    public function updatePassword(Request $request)
    {
        Validator::make($request->all(), [
            'password' => 'current_password',
            'new_password' => ['required', 'confirmed', Password::min(8)],
        ])->validate();

        $user = Auth::user();
        $user->password = $request->get('new_password');
        $user->save();

        Session::flash('alert-success', 'Password updated successfully');

        return redirect()->route('me.change-password');
    }
}
