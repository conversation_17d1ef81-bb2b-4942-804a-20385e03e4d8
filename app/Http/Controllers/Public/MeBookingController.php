<?php

namespace App\Http\Controllers\Public;

use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\DataTables;

class MeBookingController extends Controller
{
    public function index()
    {
        return view('public.me.booking');
    }

    public function query()
    {
        $query = Auth::user()->bookings()->with(['user', 'service.translations']);

        $result = DataTables::of($query)->addColumn('actions', function ($row) {
            $actions = '<a href="'.route('me.booking.show', [$row->id]).'" class="btn btn-sm btn-info mx-1 mb-1"> Show</a>';

            return $actions;
        })->editColumn('service.translations.name', function ($row) {
            return getTranslation($row->service, 'name');
        })->addColumn('status', function ($row) {

            switch ($row->status) {
                case 'Pending':
                    return '<span class="badge bg-warning">Pending</span>';
                case 'Paid':
                    return '<span class="badge bg-success">Paid</span>';
                case 'Failed':
                    return '<span class="badge bg-danger">Failed</span>';
                case 'Cancelled':
                    return '<span class="badge bg-danger">Cancelled</span>';
                    break;
            }
        })->editColumn('created_at', function ($row) {
            return $row->created_at;
        })->editColumn('date', function ($row) {
            return $row->date->format('Y-m-d');
        })->addColumn('paid_at', function ($row) {
            return $row->paid_at ?? '-';
        })->addColumn('prepared_at', function ($row) {
            return $row->prepared_at ?? '-';
        })->addColumn('shipped_at', function ($row) {
            return $row->shipped_at ?? '-';
        })->addColumn('delivered_at', function ($row) {
            return $row->delivered_at ?? '-';
        })->addColumn('failed_at', function ($row) {
            return $row->failed_at ?? '-';
        })->rawColumns(['actions', 'status'])->make(true);

        return $result;
    }
}
