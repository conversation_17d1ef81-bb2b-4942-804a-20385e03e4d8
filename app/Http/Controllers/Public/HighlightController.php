<?php

namespace App\Http\Controllers\Public;

use App\Models\Highlight;

class HighlightController extends Controller
{
    public function show(Highlight $highlight)
    {
        if (! $highlight->is_active || ($highlight->start_at && $highlight->start_at->isFuture()) || ($highlight->end_at && $highlight->end_at->isPast())) {
            return abort(404);
        }

        return view('public.highlight.show', compact('highlight'));
    }
}
