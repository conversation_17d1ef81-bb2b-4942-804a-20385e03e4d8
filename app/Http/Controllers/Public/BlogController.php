<?php

namespace App\Http\Controllers\Public;

use App\Models\Blog;

class BlogController extends Controller
{
    public function index()
    {
        $blogs = Blog::isActive()->with('translations')->orderBy('published_at')->get();

        return view('public.blog.index', compact('blogs'));
    }

    public function show(Blog $blog)
    {
        if (! $blog->is_active) {
            return abort(404);
        }

        $blog->load('translations');

        $next = Blog::isActive()->with('translations')->where('id', '>', $blog->id)->orderBy('id')->first();
        $previous = Blog::isActive()->with('translations')->where('id', '<', $blog->id)->orderByDesc('id')->first();

        return view('public.blog.show', compact('blog', 'next', 'previous'));
    }
}
