<?php

namespace App\Http\Controllers\Public;

use App\Models\Booking;
use App\Models\Category;
use App\Models\Group;
use App\Models\Payment;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ServiceController extends Controller
{
    public function index()
    {
        $categories = Category::isActive()->isService()->orderBy('sequence')->with(['services' => function ($query) {
            $query->isActive()->with('translations');
        }])->get()->each(function ($category) {
            $category->services = $category->services->groupBy('group.slug');
        });

        return view('public.service.index', compact('categories'));
    }

    public function selectCompany(Category $category, $group)
    {
        $group = Group::where('slug', $group)->firstOrFail();
        $services = $category->services()->isActive()->with('translations')->where('group_id', $group->id)->get();

        if ($services->count() === 1) {
            return redirect()->route('service.show', [$category->slug, $group->slug, $services->first()->slug]);
        } else {
            return view('public.service.select-company', compact('category', 'group', 'services'));
        }
    }

    public function show(Category $category, $group, Service $service)
    {
        $group = Group::where('slug', $group)->firstOrFail();
        $service = $category->services()->isActive()->with('translations')->where('group_id', $group->id)->where('slug', $service->slug)->firstOrFail();
        $services = $category->services()->isActive()->with('translations')->where('group_id', $group->id)->where('id', '!=', $service->id)->get();
        $groups = $category->services()->isActive()->with('translations')->where('group_id', '!=', $group->id)->get()->groupBy('group.slug');

        return view('public.service.show', compact('category', 'group', 'service', 'services', 'groups'));
    }

    public function booking(Category $category, $group, Service $service)
    {
        $group = Group::where('slug', $group)->firstOrFail();
        $service = $category->services()->isActive()->with('translations')->where('group_id', $group->id)->where('slug', $service->slug)->firstOrFail();

        $dates = $service->company->timeSlots()->whereDate('date', '>', Carbon::now()->format('Y-m-d'))
            ->whereMonth('date', Carbon::now()->format('m'))
            ->whereYear('date', Carbon::now()->format('Y'))->where('is_booked', false)->get()->groupBy(function ($timeSlot) {
                return Carbon::parse($timeSlot->date)->format('Y-m-d');
            });

        $dates = $dates->map(function ($_, $key) {
            return $key = Carbon::parse($key)->format('Y-m-d');
        })->toArray();

        return view('public.service.booking', compact('category', 'group', 'service', 'dates'));
    }

    public function getSlots(Service $service)
    {
        $date = request()->get('date');
        $timeSlots = $service->company->timeSlots()->whereDate('date', $date)->where('is_booked', false)->get();

        return response()->json($timeSlots);
    }

    public function checkout(Category $category, $group, Service $service, Request $request)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'phone_e164' => 'nullable|phone',
            'date' => 'required',
            'time' => 'required',
        ])->validate();

        $group = Group::where('slug', $group)->firstOrFail();
        $timeSlot = $service->company->timeSlots()->where('id', $request->get('time'))->first();
        $service = $category->services()->isActive()->with(['translations', 'group.translations'])->where('group_id', $group->id)->where('slug', $service->slug)->first();

        $booking = new Booking;

        try {
            DB::beginTransaction();

            if ($timeSlot->is_booked == true) {
                DB::rollBack();

                return redirect()->back()->withErrors(['time' => 'This time slot is not available.'])->withInput();
            }

            if ($timeSlot->is_booked == true && $timeSlot->booking) {
                DB::rollBack();

                return redirect()->back()->withErrors(['time' => 'This time slot is not available.'])->withInput();
            }

            if (! $service) {
                DB::rollBack();

                return redirect()->back()->withErrors(['time' => 'This service is not available.'])->withInput();
            }

            $booking->name = $request->get('name');
            $booking->email = $request->get('email');
            $booking->phone_e164 = $request->get('phone_e164');
            $booking->date = $request->get('date');
            $booking->time = $timeSlot->time;
            $booking->time_slot_id = $timeSlot->id;
            $booking->time_slot_meta = $timeSlot;
            $booking->service_id = $service->id;
            $booking->service_meta = $service;

            if (Auth::check()) {
                $booking->user()->associate(Auth::user());
                $booking->user_meta = Auth::user();
            }

            $booking->sub_total = $service->price;
            $booking->discount = 0;
            $booking->total = $service->price;
            $booking->tax_name = env('TAX_NAME', 'SST 6%');
            $booking->tax = $booking->total * env('TAX_RATE', 0);
            $booking->grand_total = $booking->total + $booking->tax;
            $booking->save();

            $payment = new Payment;
            $payment->amount = $booking->grand_total;
            $payment->method = env('APP_ENV') == 'production' ? 'fiuu' : 'simulator';
            $payment->payable()->associate($booking);
            $payment->save();

            $timeSlot->is_booked = true;
            $timeSlot->save();

            // if ($payment->method == 'fiuu') {
            //     $payment->url = route('fiuu-payment-gateway', $payment);
            // } else {
            $payment->url = route('fake-payment', $payment);
            // }
            $payment->save();

            DB::commit();

            return redirect($payment->url);
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()->withErrors(['time' => $e->getMessage()])->withInput();
        }
    }
}
