<?php

namespace App\Http\Controllers\Public;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Password;

class ForgotPasswordController extends Controller
{
    public function __invoke(Request $request)
    {
        return view('public.forgot-password');

        // Validator::make($request->all(), [
        //     'email' => 'required|email',
        // ])->validate();

        // $user = User::where('email', $request->get('email'))->whereNotNull('email_verified_at')->first();

        // if (!$user) {
        //     return response(['message' => "Email doesn't exist"], 422);
        // }

        // $status = Password::sendResetLink($request->only('email'));
    }

    public function request(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => $validator->errors()->first()]);
        }

        $user = User::where('email', $request->get('email'))->whereNotNull('email_verified_at')->first();

        if (! $user) {
            return redirect()->back()->withInput()->withErrors(['auth' => "Email doesn't exist"]);
        }

        Session::flash('alert-success', 'Reset password link has been sent to your email');
        Password::sendResetLink($request->only('email'));

        return redirect()->route('login');
    }
}
