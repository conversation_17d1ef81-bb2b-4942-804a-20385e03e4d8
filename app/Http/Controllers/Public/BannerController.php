<?php

namespace App\Http\Controllers\Public;

use App\Models\Banner;

class BannerController extends Controller
{
    public function show(Banner $banner)
    {
        if (! $banner->is_active || ($banner->start_at && $banner->start_at->isFuture()) || ($banner->end_at && $banner->end_at->isPast())) {
            return abort(404);
        }

        return view('public.banner.show', compact('banner'));
    }
}
