<?php

namespace App\Http\Controllers\Public;

use App\Models\Banner;
use App\Models\Blog;
use App\Models\Category;
use App\Models\Highlight;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class HomeController extends Controller
{
    public function home()
    {
        $banners = Banner::isActive()->isPublic()->orderBy('sequence')->get();
        $highlights = Highlight::isActive()->isPublic()->orderBy('sequence')->get();
        $blogs = Blog::isActive()->orderBy('published_at')->limit(5)->get();

        $categories = Category::isActive()->isService()->orderBy('sequence')->with(['services' => function ($query) {
            $query->isActive();
        }])->limit(4)->get()->each(function ($category) {
            $category->services = $category->services->groupBy('group.slug');
        });

        return view('public.home', compact('banners', 'highlights', 'blogs', 'categories'));
    }

    public function about()
    {
        return view('public.about');
    }

    public function blog()
    {
        return view('public.blog');
    }

    public function blogDetail($id)
    {
        return view('public.blog-detail');
    }

    public function contact()
    {
        return view('public.contact');
    }

    public function purchaseGuide()
    {
        return view('public.purchase-guide');
    }

    public function privacyPolicy()
    {
        return view('public.privacy-policy');
    }

    public function termsOfService()
    {
        return view('public.terms-of-service');
    }

    public function changeLanguage($language)
    {
        Session::put('locale', $language);
        App::setLocale($language);

        return back();
    }
}
