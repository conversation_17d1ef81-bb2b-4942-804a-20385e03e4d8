<?php

namespace App\Http\Controllers\Public;

use App\Models\User;
use Illuminate\Http\Request;

class EmailVerificationController extends Controller
{
    public function verify(Request $request, $id)
    {
        // Prevent HEAD method checking to help with email verification from email client

        if ($request->method() === 'GET') {

            $user = User::whereNull('email_verified_at')->find($id);

            if ($user && ! $user->hasVerifiedEmail()) {
                $user->markEmailAsVerified();
            }

            return view('auth.email-verification-success');
        }
    }
}
