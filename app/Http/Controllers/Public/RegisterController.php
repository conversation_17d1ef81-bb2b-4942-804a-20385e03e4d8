<?php

namespace App\Http\Controllers\Public;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class RegisterController extends Controller
{
    public function register(Request $request)
    {
        return view('public.register');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'phone_e164' => 'nullable|phone|unique:users,phone_e164',
            'password' => ['required', 'confirmed', Password::min(8)],
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => $validator->errors()->first()]);
        }

        $user = User::withTrashed()->firstOrNew(['email' => $request->get('email')]);
        if ($user->email_verified_at) {
            return redirect()->back()->withInput()->withErrors(['auth' => 'This email has been taken']);
        }

        $user->fill($request->only('name', 'email', 'phone_e164'));
        $user->password = $request->get('password');
        $user->save();

        Session::flash('alert-success', 'Your account has been created. Please verify your email');
        $user->sendEmailVerificationNotification();

        return redirect()->route('login');
    }
}
