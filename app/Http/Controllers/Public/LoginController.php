<?php

namespace App\Http\Controllers\Public;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    public function index()
    {
        return view('public.login');
    }

    public function authenticate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors(['auth' => 'Please fill in required fields']);
        } else {
            $credentials = $request->only('email', 'password');
            if (Auth::attempt($credentials, $request->get('remember'))) {
                $user = Auth::user();

                if ($user->email_verified_at === null) {
                    Auth::logout();

                    return redirect()->back()->withInput()->withErrors(['auth' => 'Please verify your email']);
                }

                if ($user->is_active === 0) {
                    Auth::logout();

                    return redirect()->back()->withInput()->withErrors(['auth' => 'Your account is inactive']);
                }

                return redirect()->intended(route('me.booking'));
            } else {
                return redirect()->back()->withInput()->withErrors(['auth' => 'Incorrect Email or Password']);
            }
        }
    }

    public function logout()
    {
        Auth::logout();

        return redirect()->route('home');
    }
}
