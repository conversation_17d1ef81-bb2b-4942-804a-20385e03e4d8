<?php

namespace App\Imports\Company\TimeSlot;

use App\Models\Company;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class UpdateOrCreate implements ToCollection, WithHeadingRow, WithMapping, WithStartRow, WithValidation
{
    protected $date;

    protected $company;

    public function __construct(Carbon $date, Company $company)
    {
        $this->company = $company;
        $this->date = $date;
    }

    public function rules(): array
    {
        return [
            'date' => [
                'required',
                'date_format:Y-m-d',
                function ($attribute, $value, $onFailure) {
                    if (Carbon::parse($value)->month != $this->date->month || Carbon::parse($value)->year != $this->date->year) {
                        $onFailure('The date must be in the month of '.$this->date->month.' and year of '.$this->date->year);
                    }
                },
            ],
            'time' => [
                'required',
            ],
        ];
    }

    public function collection($collection)
    {

        $detach_ids = $this->company->timeSlots->pluck('id')->diff($collection->pluck('id'));

        if ($detach_ids->count() > 0) {

            $this->company->timeSlots()->whereIn('id', $detach_ids)->get()->each(function ($timeSlot) {
                if ($timeSlot->booking && $timeSlot->is_booked) {
                    throw new \Maatwebsite\Excel\Validators\ValidationException(\Illuminate\Validation\ValidationException::withMessages(['cannot delete time slot with booking']), []);
                } else {
                    $timeSlot->delete();
                }
            });
        }

        foreach ($collection as $key => $row) {
            if ($row['id']) {
                $timeSlot = $this->company->timeSlots()->find($row['id']);
                if ($timeSlot) {
                    $timeSlot->update([
                        'date' => $row['date'],
                        'time' => $row['time'],
                        'is_booked' => $row['is_booked'],
                    ]);
                }
            } else {
                $this->company->timeSlots()->create([
                    'date' => $row['date'],
                    'time' => $row['time'],
                    'is_booked' => $row['is_booked'],
                ]);
            }
        }
    }

    public function map($row): array
    {
        return [
            'id' => $row['id'],
            'date' => is_numeric($row['date']) ? Carbon::createFromTimestamp(($row['date'] - 25569) * 86400)->format('Y-m-d') : false,
            'time' => $row['time'],
            'is_booked' => $row['is_booked'],
        ];
    }

    public function startRow(): int
    {
        return 2;
    }
}
