$(document).ready(function () {
    $(".datepicker").flatpickr({
        disableMobile: true,
    });

    $(".datetimepicker").flatpickr({
        enableTime: true,
        dateFormat: "Y-m-d H:i",
    });

    const inputs = document.querySelectorAll(".phone_e164");
    inputs.forEach(function (input) {
        window.intlTelInput(input, {
            containerClass: "is-invalid",
            utilsScript:
                "/dashboard-assets/plugins/custom/intl-tel-input/utils.js",
            onlyCountries: ["my", "th", "sg", "cn"],
            showSelectedDialCode: true,
            hiddenInput: function (telInputName) {
                return {
                    phone: telInputName,
                    country: telInputName + "_country",
                };
            },
        });
    });

    $("select[name='identity_type']").change(function () {
        if ($(this).val() == "Malaysia IC") {
            Inputmask({
                mask: "999999-99-9999",
            }).mask("input[name='identity_no']");
        } else {
            Inputmask({
                mask: null,
            }).mask("input[name='identity_no']");
        }
    });

    $("select[name='identity_type']").trigger("change");
});

Object.assign(DataTable.defaults, {
    dom:
        "<'row'" +
        "<'col-sm-6 d-flex align-items-center justify-conten-start'l>" +
        "<'col-sm-6 d-flex align-items-center justify-content-end'f>" +
        ">" +
        "<'table-responsive'tr>" +
        "<'row'" +
        "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
        "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>" +
        ">",
});
