/*
@File: Psylo Template Styles

* This file contains the styling for the actual template, this
is the file you need to edit to change the look of the
template.

This files table contents are outlined below>>>>>

*******************************************
*******************************************

** - Default CSS
** - Default btn CSS
** - Section Title CSS
** - Preloader Area CSS
** - Top Header Area CSS
** - Navbar Area CSS
** - Search Layout CSS
** - Main Banner Area CSS
** - Main Slides Area CSS
** - Features Area CSS
** - About Area CSS
** - Partner Area CSS
** - Offer Area CSS
** - Reviews Area CSS
** - Philosophy Area CSS
** - Products Area CSS
** - Blog Area CSS
** - Services Area CSS
** - FAQS Area CSS
** - Fun Facts Area CSS
** - Pricing Area CSS
** - Download Area CSS
** - Page Banner Area CSS
** - Coaches Area CSS
** - Coaches Details Area CSS
** - Events Area CSS
** - Events Details Area CSS
** - Success Story Area CSS
** - Courses Area CSS
** - Membership Levels Area CSS
** - Become Coaches Area CSS
** - Courses Details Area CSS
** - Services Details Area CSS
** - Cart Area CSS
** - Wishlist Area CSS
** - Checkout Area CSS
** - My Account Area CSS
** - Products Details Area CSS
** - Blog Details Area CSS
** - Purchase Guide Area CSS
** - Book Online Area CSS
** - Gallery Area CSS
** - Book Appointment Area CSS
** - 404 Error Area CSS
** - Privacy Policy Area CSS
** - Terms of Service Area CSS
** - Coming Soon Area CSS
** - Contact Info Area CSS
** - Contact Area CSS
** - Pagination Area CSS
** - Subscribe Area CSS
** - Footer Area CSS
** - Copy Right Area CSS
** - Go Top CSS

*/
/*================================================
Default CSS
=================================================*/
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
body {
  padding: 0;
  margin: 0;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
}

a {
  text-decoration: none;
  transition: 0.6s;
  color: #212529;
  display: inline-block;
}
a:hover, a:focus {
  color: #000000;
  text-decoration: none;
}

button {
  outline: 0 !important;
}

p {
  margin-bottom: 15px;
  line-height: 1.8;
  color: #6b6b84;
  font-weight: 500;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
}
p:last-child {
  margin-bottom: 0;
}

i {
  line-height: normal;
}

.d-table {
  width: 100%;
  height: 100%;
}
.d-table-cell {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
  display: inline-block;
}

.ptb-100 {
  padding-top: 100px;
  padding-bottom: 100px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-70 {
  padding-bottom: 70px;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  color: #212529;
  font-family: "Poppins", sans-serif;
  font-weight: bold;
}
.h1:last-child, .h2:last-child, .h3:last-child, .h4:last-child, .h5:last-child, .h6:last-child, h1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child {
  margin-bottom: 0;
}

/*================================================
Default Btn CSS
=================================================*/
.default-btn {
  display: inline-block;
  padding: 15px 60px 15px 10px;
  border-radius: 10px 10px 0 10px;
  font-size: 15px;
  font-weight: 500;
  background-color: #000000;
  color: #ffffff;
  transition: 0.6s;
  position: relative;
}
.default-btn i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  display: inline-block;
  height: 38px;
  width: 38px;
  line-height: 38px;
  color: #000000;
  border-radius: 10px 10px 0 10px;
  background-color: #ffffff;
  transition: 0.6s;
}
.default-btn:hover {
  background: #6c757d;
  color: #ffffff;
}
.default-btn:hover i {
  background-color: #ffffff;
  color: #000000;
}

.optional-btn {
  display: inline-block;
  padding: 15px 60px 14px 15px;
  border-radius: 10px 10px 0 10px;
  font-size: 15px;
  font-weight: 500;
  background-color: #ffffff;
  color: #212529;
  transition: 0.6s;
  position: relative;
}
.optional-btn i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  display: inline-block;
  height: 38px;
  width: 38px;
  line-height: 38px;
  color: #ffffff;
  border-radius: 10px 10px 0 10px;
  background-color: #6c757d;
  transition: 0.6s;
}
.optional-btn:hover {
  background: #000000;
  color: #ffffff;
}
.optional-btn:hover i {
  background-color: #ffffff;
  color: #212529;
}

/*================================================
Section Title CSS
=================================================*/
.section-title {
  text-align: center;
  margin-bottom: 45px;
}
.section-title h2 {
  font-size: 45px;
  margin-bottom: 20px;
}
.section-title h2 span {
  font-weight: 400;
  font-style: italic;
}
.section-title p {
  max-width: 650px;
  margin: auto;
}

/*================================================
Preloader Area CSS
=================================================*/
.preloader-area {
  position: fixed;
  top: 0;
  background-color: #000000;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  text-align: center;
}
.preloader-area .spinner {
  width: 4em;
  height: 4em;
  transform: perspective(20em) rotateX(-24deg) rotateY(20deg) rotateZ(30deg);
  transform-style: preserve-3d;
  position: absolute;
  left: 0;
  right: 0;
  top: 45%;
  transform: translateY(-45%);
  margin-left: auto;
  margin-right: auto;
}
.preloader-area .spinner .disc {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  border: 0.3em dotted #ffffff;
}
.preloader-area .spinner .disc:nth-child(1) {
  animation: rotate 12s linear infinite;
}
.preloader-area .spinner .disc:nth-child(2) {
  animation: rotateDisc2 12s linear infinite;
}
.preloader-area .spinner .disc:nth-child(3) {
  animation: rotateDisc3 12s linear infinite;
}
.preloader-area .spinner .inner {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: sphereSpin 6s linear infinite;
}
.preloader-area .spinner::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 50%;
  right: 0;
  bottom: 0;
  border: 2px dotted #ffffff;
  margin: -15px;
}

@keyframes sphereSpin {
  0% {
    transform: rotateX(360deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(0deg) rotateY(360deg);
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes rotateDisc2 {
  from {
    transform: rotateX(90deg) rotateZ(0deg);
  }
  to {
    transform: rotateX(90deg) rotateZ(360deg);
  }
}
@keyframes rotateDisc3 {
  from {
    transform: rotateY(90deg) rotateZ(0deg);
  }
  to {
    transform: rotateY(90deg) rotateZ(360deg);
  }
}
/*================================================
Top Header Area CSS
=================================================*/
.top-header-area {
  background-color: #212529;
  padding-top: 15px;
  padding-bottom: 15px;
}
.top-header-area.bg-transparent {
  border-bottom: 1px solid #eeeeee;
}
.top-header-area.bg-transparent .top-header-information li {
  color: #6c757d;
}
.top-header-area.bg-transparent .top-header-information li a {
  color: #6c757d;
}
.top-header-area.bg-transparent .top-header-information li a:hover {
  color: #000000;
}
.top-header-area.bg-transparent .top-header-information li i {
  color: #6c757d;
}
.top-header-area.bg-transparent .top-header-optional li a::before {
  background-color: rgba(108, 117, 125, 0.368627451);
}
.top-header-area.bg-transparent .top-header-optional li a i {
  color: #ffffff;
}
.top-header-area.bg-transparent .top-header-optional li a i:hover {
  transform: translateY(-2px);
  color: #000000;
}
.top-header-area.bg-color {
  background-color: #000000;
}
.top-header-area.bg-color .top-header-information li {
  color: #ffffff;
}
.top-header-area.bg-color .top-header-information li a {
  color: #ffffff;
}
.top-header-area.bg-color .top-header-information li a:hover {
  color: #6c757d;
}
.top-header-area.bg-color .top-header-information li i {
  color: #6c757d;
}
.top-header-area.bg-color .top-header-optional li a::before {
  background-color: rgba(108, 117, 125, 0.368627451);
}
.top-header-area.bg-color .top-header-optional li a i {
  color: #6c757d;
}
.top-header-area.bg-color .top-header-optional li a i:hover {
  transform: translateY(-2px);
  color: #ffffff;
}

.top-header-information {
  padding-left: 0;
  margin-bottom: 0;
}
.top-header-information li {
  color: #ffffff;
  font-size: 15px;
  font-weight: 500;
  list-style-type: none;
  display: inline-block;
  position: relative;
  margin-right: 15px;
  padding-left: 22px;
}
.top-header-information li:last-child {
  margin-right: 0;
}
.top-header-information li a {
  color: #ffffff;
}
.top-header-information li a:hover {
  color: #000000;
}
.top-header-information li i {
  position: absolute;
  left: 0;
  top: 3px;
  color: #000000;
}

.top-header-optional {
  padding-left: 0;
  margin-bottom: 0;
  text-align: right;
}
.top-header-optional li {
  list-style-type: none;
  display: inline-block;
}
.top-header-optional li a {
  margin-right: 12px;
  position: relative;
}
.top-header-optional li a::before {
  position: absolute;
  right: -8px;
  top: 3px;
  width: 1px;
  content: "";
  height: 18px;
  background-color: rgba(218, 218, 218, 0.3490196078);
}
.top-header-optional li a:last-child {
  margin-right: 0;
}
.top-header-optional li a:last-child::before {
  display: none;
}
.top-header-optional li a i {
  color: #ffffff;
  font-size: 20px;
  transition: 0.6s;
}
.top-header-optional li a i:hover {
  transform: translateY(-2px);
  color: #000000;
}

.header-information {
  display: none;
  background-color: #000000;
  color: #ffffff;
  cursor: pointer;
  padding: 15px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

/*================================================
Navbar Area CSS
=================================================*/
.main-responsive-nav {
  display: none;
}

.main-navbar {
  padding: 10px 0;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
}
.main-navbar .navbar {
  transition: 0.6s;
  padding: 0;
}
.main-navbar .navbar .navbar-brand {
  padding: 0;
}
.main-navbar .navbar ul {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 0;
}
.main-navbar .navbar .navbar-nav .nav-item {
  position: relative;
  margin-left: 15px;
  margin-right: 15px;
}
.main-navbar .navbar .navbar-nav .nav-item a {
  color: #212529;
  font-size: 16px;
  font-weight: 600;
  padding-left: 0;
  padding-right: 0;
  padding-top: 25px;
  padding-bottom: 20px;
  transition: 0.6s;
}
.main-navbar .navbar .navbar-nav .nav-item a i {
  font-size: 15px;
  position: relative;
  top: 2px;
  display: inline-block;
  margin-left: -5px;
  margin-right: -5px;
}
.main-navbar .navbar .navbar-nav .nav-item a:hover, .main-navbar .navbar .navbar-nav .nav-item a:focus, .main-navbar .navbar .navbar-nav .nav-item a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item:last-child {
  margin-right: 0;
}
.main-navbar .navbar .navbar-nav .nav-item:first-child {
  margin-left: 0;
}
.main-navbar .navbar .navbar-nav .nav-item:hover a, .main-navbar .navbar .navbar-nav .nav-item.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu {
  border: none;
  top: 70px;
  left: 0;
  z-index: 99;
  opacity: 0;
  width: 250px;
  display: block;
  border-radius: 0;
  padding: 10px 0;
  position: absolute;
  visibility: hidden;
  opacity: 0;
  margin-top: 10px;
  background: #ffffff;
  transition: 0.6s;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li {
  margin: 0;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li a {
  padding: 8px 20px;
  position: relative;
  display: block;
  color: #212529;
  font-size: 15px;
  font-weight: 600;
  transition: 0.6s;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li a i {
  margin: 0;
  position: absolute;
  top: 50%;
  font-size: 15px;
  transform: translateY(-50%);
  right: 15px;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li a.active {
  color: #000000;
  letter-spacing: 1px;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  right: -250px;
  left: auto;
  visibility: hidden;
  transform: translateY(5px);
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  left: 250px;
  visibility: hidden;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  left: -250px;
  visibility: hidden;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  left: 250px;
  visibility: hidden;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  left: -250px;
  visibility: hidden;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu {
  top: 0;
  opacity: 0;
  left: 250px;
  visibility: hidden;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a {
  color: #212529;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:hover, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a:focus, .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li a.active {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li.active a {
  color: #000000;
}
.main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.main-navbar .navbar .navbar-nav .nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}
.main-navbar .navbar .others-options .option-item {
  margin-right: 25px;
}
.main-navbar .navbar .others-options .option-item:last-child {
  margin-right: 0;
}
.main-navbar .navbar .others-options .option-item .cart-btn {
  position: relative;
  top: 5px;
}
.main-navbar .navbar .others-options .option-item .cart-btn a {
  font-size: 25px;
  color: #212529;
  transition: 0.6s;
  position: relative;
}
.main-navbar .navbar .others-options .option-item .cart-btn a:hover {
  color: #000000;
}
.main-navbar .navbar .others-options .option-item .cart-btn a span {
  position: absolute;
  right: -5px;
  top: 0;
  width: 15px;
  height: 15px;
  text-align: center;
  line-height: 15px;
  border-radius: 50%;
  background: #212529;
  color: #ffffff;
  font-size: 10px;
}
.main-navbar .navbar .others-options .option-item .search-box {
  position: relative;
  top: 2px;
}
.main-navbar .navbar .others-options .option-item .search-box i {
  display: inline-block;
  font-size: 25px;
  color: #212529;
  transition: 0.6s;
  cursor: pointer;
}
.main-navbar .navbar .others-options .option-item .search-box i:hover {
  color: #000000;
}
.main-navbar .navbar .others-options .option-item .navbar-btn .default-btn {
  display: inline-block;
  padding: 15px 60px 15px 15px;
  border-radius: 10px 10px 0 10px;
  font-size: 16px;
  font-weight: 600;
  background-color: #212529;
  color: #ffffff;
  transition: 0.6s;
  position: relative;
}
.main-navbar .navbar .others-options .option-item .navbar-btn .default-btn i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  display: inline-block;
  height: 38px;
  width: 38px;
  line-height: 38px;
  color: #ffffff;
  border-radius: 10px 10px 0 10px;
  background-color: #000000;
  transition: 0.6s;
}
.main-navbar .navbar .others-options .option-item .navbar-btn .default-btn:hover {
  background: #000000;
  color: #212529;
}
.main-navbar .navbar .others-options .option-item .navbar-btn .default-btn:hover i {
  background-color: #ffffff;
  color: #212529;
}

.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a {
  color: #ffffff;
}
.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a:hover {
  color: #000000;
}
.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a span {
  background: #ffffff;
  color: #000000;
}
.p-relative-color .main-navbar .navbar .others-options .option-item .search-box i {
  color: #ffffff;
}
.p-relative-color .main-navbar .navbar .others-options .option-item .search-box i:hover {
  color: #000000;
}

.navbar-two .main-navbar .navbar .others-options {
  margin-left: 30px;
}

.navbar-area.is-sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  background: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  transition: 0.6s;
}
.navbar-area.is-sticky.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a {
  color: #212529;
}
.navbar-area.is-sticky.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a:hover {
  color: #000000;
}
.navbar-area.is-sticky.p-relative-color .main-navbar .navbar .others-options .option-item .cart-btn a span {
  background: #212529;
  color: #000000;
}
.navbar-area.is-sticky.p-relative-color .main-navbar .navbar .others-options .option-item .search-box i {
  color: #212529;
}
.navbar-area.is-sticky.p-relative-color .main-navbar .navbar .others-options .option-item .search-box i:hover {
  color: #000000;
}

.others-option-for-responsive {
  display: none;
}
.others-option-for-responsive .dot-menu {
  padding: 0 10px;
  height: 30px;
  cursor: pointer;
  z-index: 9991;
  position: absolute;
  right: 60px;
  top: -28px;
}
.others-option-for-responsive .dot-menu .inner {
  display: flex;
  align-items: center;
  height: 30px;
}
.others-option-for-responsive .dot-menu .inner .circle {
  height: 5px;
  width: 5px;
  border-radius: 100%;
  margin: 0 2px;
  transition: 0.6s;
  background-color: #212529;
}
.others-option-for-responsive .dot-menu:hover .inner .circle {
  background-color: #000000;
}
.others-option-for-responsive .container {
  position: relative;
}
.others-option-for-responsive .container .container {
  position: absolute;
  right: 0;
  top: 10px;
  max-width: 260px;
  background-color: #ffffff;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
  margin-left: auto;
  opacity: 0;
  visibility: hidden;
  transition: 0.6s;
  transform: scaleX(0);
  z-index: 2;
  padding-left: 15px;
  padding-right: 15px;
}
.others-option-for-responsive .container .container.active {
  opacity: 1;
  visibility: visible;
  transform: scaleX(1);
}
.others-option-for-responsive .option-inner .others-options .option-item {
  margin-right: 25px;
}
.others-option-for-responsive .option-inner .others-options .option-item:last-child {
  margin-right: 0;
}
.others-option-for-responsive .option-inner .others-options .option-item .cart-btn {
  position: relative;
  top: 5px;
}
.others-option-for-responsive .option-inner .others-options .option-item .cart-btn a {
  font-size: 25px;
  color: #212529;
  transition: 0.6s;
  position: relative;
}
.others-option-for-responsive .option-inner .others-options .option-item .cart-btn a:hover {
  color: #000000;
}
.others-option-for-responsive .option-inner .others-options .option-item .cart-btn a span {
  position: absolute;
  right: -5px;
  top: 0;
  width: 15px;
  height: 15px;
  text-align: center;
  line-height: 15px;
  border-radius: 50%;
  background: #212529;
  color: #ffffff;
  font-size: 10px;
}
.others-option-for-responsive .option-inner .others-options .option-item .search-box {
  position: relative;
  top: 2px;
}
.others-option-for-responsive .option-inner .others-options .option-item .search-box i {
  display: inline-block;
  font-size: 25px;
  color: #212529;
  transition: 0.6s;
  cursor: pointer;
}
.others-option-for-responsive .option-inner .others-options .option-item .search-box i:hover {
  color: #000000;
}
.others-option-for-responsive .option-inner .others-options .option-item .navbar-btn .default-btn {
  display: inline-block;
  padding: 15px 60px 15px 15px;
  border-radius: 10px 10px 0 10px;
  font-size: 16px;
  font-weight: 600;
  background-color: #212529;
  color: #ffffff;
  transition: 0.6s;
  position: relative;
}
.others-option-for-responsive .option-inner .others-options .option-item .navbar-btn .default-btn i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  display: inline-block;
  height: 38px;
  width: 38px;
  line-height: 38px;
  color: #ffffff;
  border-radius: 10px 10px 0 10px;
  background-color: #000000;
  transition: 0.6s;
}
.others-option-for-responsive .option-inner .others-options .option-item .navbar-btn .default-btn:hover {
  background: #000000;
  color: #212529;
}
.others-option-for-responsive .option-inner .others-options .option-item .navbar-btn .default-btn:hover i {
  background-color: #ffffff;
  color: #212529;
}

.main-header-area {
  position: absolute;
  width: 100%;
  z-index: 9;
}

/*================================================
Search Layout CSS
=================================================*/
.search-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}
.search-overlay .search-overlay-layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  transform: translateX(100%);
}
.search-overlay .search-overlay-layer:nth-child(1) {
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease-in-out 0s;
}
.search-overlay .search-overlay-layer:nth-child(2) {
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease-in-out 0.3s;
}
.search-overlay .search-overlay-layer:nth-child(3) {
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  transition: all 0.9s ease-in-out 0.6s;
}
.search-overlay .search-overlay-close {
  position: absolute;
  top: 40px;
  right: 40px;
  width: 50px;
  z-index: 2;
  text-align: center;
  cursor: pointer;
  padding: 10px;
  transition: all 0.9s ease-in-out 1.5s;
  opacity: 0;
  visibility: hidden;
}
.search-overlay .search-overlay-close .search-overlay-close-line {
  width: 100%;
  height: 3px;
  float: left;
  margin-bottom: 5px;
  background-color: #ffffff;
  transition: all 500ms ease;
}
.search-overlay .search-overlay-close .search-overlay-close-line:nth-child(1) {
  transform: rotate(45deg);
}
.search-overlay .search-overlay-close .search-overlay-close-line:nth-child(2) {
  margin-top: -7px;
  transform: rotate(-45deg);
}
.search-overlay .search-overlay-close:hover .search-overlay-close-line {
  background: #000000;
  transform: rotate(180deg);
}
.search-overlay .search-overlay-form {
  transition: all 0.9s ease-in-out 1.4s;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateY(-50%) translateX(-50%);
  z-index: 2;
  max-width: 500px;
  width: 500px;
}
.search-overlay .search-overlay-form form {
  position: relative;
}
.search-overlay .search-overlay-form form .input-search {
  display: block;
  width: 100%;
  height: 55px;
  border: none;
  border-radius: 30px;
  padding-left: 20px;
  color: #212529;
  font-size: 16px;
  outline: 0;
  transition: 0.6s;
}
.search-overlay .search-overlay-form form .input-search::-moz-placeholder {
  -moz-transition: 0.6s;
  transition: 0.6s;
  -moz-columns: #6b6b84;
       columns: #6b6b84;
}
.search-overlay .search-overlay-form form .input-search::placeholder {
  transition: 0.6s;
  -moz-columns: #6b6b84;
       columns: #6b6b84;
}
.search-overlay .search-overlay-form form .input-search:focus::-moz-placeholder {
  color: transparent;
}
.search-overlay .search-overlay-form form .input-search:focus::placeholder {
  color: transparent;
}
.search-overlay .search-overlay-form form button {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 45px;
  color: #ffffff;
  height: 45px;
  border-radius: 50%;
  background-color: #000000;
  transition: 0.6s;
  border: none;
  font-size: 20px;
  line-height: 45px;
  cursor: pointer;
}
.search-overlay .search-overlay-form form button:hover {
  background-color: #212529;
  color: #ffffff;
}
.search-overlay.search-overlay-active.search-overlay {
  opacity: 1;
  visibility: visible;
}
.search-overlay.search-overlay-active.search-overlay .search-overlay-layer {
  transform: translateX(0);
}
.search-overlay.search-overlay-active.search-overlay .search-overlay-close {
  opacity: 1;
  visibility: visible;
}
.search-overlay.search-overlay-active.search-overlay .search-overlay-form {
  opacity: 1;
  visibility: visible;
}

@media only screen and (max-width: 1199px) {
  .main-responsive-nav {
    display: block;
  }
  .main-responsive-nav .main-responsive-menu {
    position: relative;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .mean-nav ul {
    font-size: 15px;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .mean-nav ul li a.active {
    color: #000000;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .mean-nav ul li a i {
    display: none;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .mean-nav ul li li a {
    font-size: 14px;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .others-options {
    display: none !important;
  }
  .main-responsive-nav .main-responsive-menu.mean-container .navbar-nav {
    overflow-y: scroll;
    height: 300px;
    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);
  }
  .main-responsive-nav .mean-container a.meanmenu-reveal {
    color: #212529;
  }
  .main-responsive-nav .mean-container a.meanmenu-reveal span {
    background: #212529;
    position: relative;
  }
  .main-responsive-nav .logo {
    position: relative;
  }
  .main-responsive-nav .logo img {
    max-width: 100px !important;
  }
  .navbar-area {
    background-color: #ffffff;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .main-navbar {
    display: none;
  }
  .others-option-for-responsive {
    display: block;
  }
  .header-information {
    display: block;
  }
  .top-header-area {
    display: none;
  }
  .top-header-area.active {
    display: block;
  }
}
/*================================================
Main Banner Area CSS
=================================================*/
.main-banner-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.main-banner-area.main-banner-video-area .background-video {
  position: absolute;
  left: 0;
  top: 0;
  width: auto;
  height: auto;
  min-height: 100%;
  min-width: 100%;
  z-index: -2;
}
.main-banner-area.main-banner-video-area .main-banner-item {
  background-image: none;
  position: relative;
  overflow: hidden;
  z-index: 3;
}

.main-banner-item {
  background-image: url(../../assets/images/main-banner/banner-bg-1.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  padding-top: 150px;
  padding-bottom: 270px;
}

.main-banner-content {
  max-width: 825px;
}
.main-banner-content h1 {
  font-size: 75px;
  color: #ffffff;
  margin-bottom: 15px;
  line-height: 1.4;
  font-weight: bold;
}
.main-banner-content h1 span {
  font-weight: 400;
  font-style: italic;
}
.main-banner-content p {
  color: #ffffff;
}
.main-banner-content .banner-btn {
  margin-top: 38px;
}
.main-banner-content .banner-btn .default-btn {
  margin-right: 20px;
}

.main-banner-item-box .container-fluid {
  padding-left: 0;
  overflow: hidden;
}
.main-banner-item-box .main-banner-content {
  max-width: 715px;
  padding-left: 50px;
}
.main-banner-item-box .main-banner-content h1 {
  font-size: 65px;
  color: #212529;
  margin-bottom: 25px;
  line-height: 1.4;
  font-weight: bold;
}
.main-banner-item-box .main-banner-content h1 span {
  font-weight: 400;
}
.main-banner-item-box .main-banner-content p {
  color: #6b6b84;
}
.main-banner-item-box .main-banner-content .banner-btn {
  margin-top: 38px;
}
.main-banner-item-box .main-banner-content .banner-btn .default-btn {
  margin-right: 20px;
}
.main-banner-item-box .main-banner-content .banner-btn .optional-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.main-banner-item-box .main-banner-content .banner-btn .optional-btn i {
  background-color: #000000;
}
.main-banner-item-box .main-banner-content .banner-btn .optional-btn:hover {
  background-color: #000000;
}
.main-banner-item-box .main-banner-content .banner-btn .optional-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.main-banner-image {
  position: relative;
}
.main-banner-image .image-shape {
  position: absolute;
  top: 0;
  right: -100px;
  animation: moveleftbounce 8s linear infinite;
  max-width: 410px;
}

.main-banner-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  opacity: 30%;
}
.main-banner-shape img {
  width: 100%;
}

.main-banner-shape-two {
  position: absolute;
  top: 0;
  right: -10px;
  z-index: -1;
  animation: moveleftbounce 10s linear infinite;
}

/*================================================
Main Slides Area CSS
=================================================*/
.main-slides-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.main-slides-item {
  background-image: url(../../assets/images/main-slides/slides-bg-1.jpg);
  background-position: center bottom;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 350px;
  padding-bottom: 350px;
}
.main-slides-item.item-two {
  background-image: url(../../assets/images/main-slides/slides-bg-2.jpg);
}
.main-slides-item.item-three {
  background-image: url(../../assets/images/main-slides/slides-bg-3.jpg);
}

.main-slides-item-box {
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.main-slides-item-box .container-fluid {
  padding-right: 0;
}
.main-slides-item-box .main-slides-content {
  max-width: 835px;
  margin-left: auto;
}

.main-slides-content {
  max-width: 825px;
}
.main-slides-content h1 {
  font-size: 65px;
  margin-bottom: 15px;
  line-height: 1.4;
  color: #6c757d;
  font-weight: bold;
  animation-delay: 0.5s;
}
.main-slides-content h1 span {
  font-weight: 400;
  font-style: italic;
}
.main-slides-content p {
  animation-delay: 1.8s;
}
.main-slides-content .slides-btn {
  margin-top: 38px;
}
.main-slides-content .slides-btn .default-btn {
  margin-right: 20px;
  animation-delay: 2s;
}
.main-slides-content .slides-btn .optional-btn {
  background-color: #6c757d;
  color: #ffffff;
  animation-delay: 2.5s;
}
.main-slides-content .slides-btn .optional-btn i {
  background-color: #000000;
}
.main-slides-content .slides-btn .optional-btn:hover {
  background-color: #000000;
}
.main-slides-content .slides-btn .optional-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.home-slides.owl-theme .owl-dots {
  margin-top: 0;
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
}
.home-slides.owl-theme .owl-dots .owl-dot {
  transition: 0.6s;
}
.home-slides.owl-theme .owl-dots .owl-dot span {
  width: 5px;
  height: 35px;
  margin: 0 8px;
  background: #000000;
  transition: 0.6s;
  border-radius: 30px;
}
.home-slides.owl-theme .owl-dots .owl-dot:hover span, .home-slides.owl-theme .owl-dots .owl-dot.active span {
  background-color: #212529;
  height: 55px;
}
.home-slides.owl-theme .owl-dots .owl-dot:hover span::before, .home-slides.owl-theme .owl-dots .owl-dot.active span::before {
  opacity: 1;
  visibility: visible;
}

/*================================================
Features Area CSS
=================================================*/
.features-area {
  position: relative;
  margin-top: -100px;
  z-index: 1;
}
.features-area.bg-ffffff {
  margin-top: 0;
}

.single-features {
  background-color: #000000;
  padding: 35px 30px 35px 30px;
  border-radius: 30px 30px 0 30px;
  transition: 0.6s;
  margin-bottom: 30px;
}
.single-features .features-title {
  position: relative;
  padding-left: 85px;
  margin-bottom: 20px;
  transition: 0.6s;
}
.single-features .features-title i {
  display: inline-block;
  height: 70px;
  width: 70px;
  line-height: 70px;
  background-color: #ffffff;
  color: #212529;
  text-align: center;
  font-size: 35px;
  border-radius: 20px 0 0 0;
  transition: 0.6s;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.single-features .features-title h3 {
  font-size: 22px;
  margin-bottom: 12px;
  font-weight: 600;
}
.single-features .features-title h3 a {
  color: #212529;
}
.single-features .features-title span {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}
.single-features p {
  font-weight: 500;
  color: #212529;
  font-size: 15px;
}
.single-features:hover {
  transform: translateY(-10px);
}

/*================================================
About Area CSS
=================================================*/
.about-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.about-area.bg-ffffff .about-main-content {
  padding-left: 0;
}

.about-main-image {
  position: relative;
}
.about-main-image img {
  border-radius: 0 50px 0 0;
}
.about-main-image .about-shape .shape-1 {
  position: absolute;
  left: -40px;
  top: 0;
  animation: moveleftbounce 5s linear infinite;
}
.about-main-image .about-shape .shape-2 {
  position: absolute;
  left: -50px;
  top: 30%;
  transform: translateY(-30%);
  max-width: 100px;
  z-index: -1;
  animation: movebounce 5s linear infinite;
}
.about-main-image .about-shape .shape-3 {
  position: absolute;
  bottom: 150px;
  left: -30px;
  animation: moveleftbounce 5s linear infinite;
}
.about-main-image .about-shape .shape-4 {
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  animation: movebounce 5s linear infinite;
}
.about-main-image .about-shape .shape-5 {
  position: absolute;
  bottom: 0;
  left: -10px;
}
.about-main-image .about-shape.about-wrap .shape-2 {
  right: -50px;
  left: auto;
  top: 0;
  transform: unset;
  max-width: 150px;
}
.about-main-image .about-shape.about-wrap .shape-3 {
  bottom: 105px;
}

@keyframes movescale {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.8);
  }
}
@keyframes movebounce {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes moveleftbounce {
  0% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(20px);
  }
  100% {
    transform: translateX(0px);
  }
}
.about-main-content {
  padding-left: 35px;
}
.about-main-content h3 {
  font-size: 35px;
  margin-bottom: 30px;
}
.about-main-content .about-content-image {
  position: relative;
  margin-bottom: 20px;
}
.about-main-content .about-content-image .sub-title {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 35px;
  font-size: 25px;
  font-style: italic;
  margin-bottom: 0;
}
.about-main-content .about-content-image .video-btn {
  display: inline-block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  background-color: #ffffff;
  border-radius: 50%;
  color: #212529;
  position: absolute;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  right: 35px;
  text-align: center;
  transition: 0.6s;
}
.about-main-content .about-content-image .video-btn i {
  font-size: 35px;
  position: relative;
  top: 8px;
  left: 5px;
  color: #6c757d;
  transition: 0.6s;
}
.about-main-content .about-content-image .video-btn::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  border: 2px solid #ffffff;
  animation: ripple 1s linear infinite;
}
.about-main-content .about-content-image .video-btn:hover {
  background-color: #6c757d;
}
.about-main-content .about-content-image .video-btn:hover i {
  color: #ffffff;
}
.about-main-content p {
  margin-bottom: 10px;
}
.about-main-content b {
  font-weight: 400;
}
.about-main-content .about-information {
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 20px 20px 20px 110px;
  border-radius: 20px 20px 0 20px;
  margin-top: 25px;
}
.about-main-content .about-information i {
  display: inline-block;
  height: 70px;
  width: 70px;
  line-height: 70px;
  background-color: #000000;
  color: #ffffff;
  text-align: center;
  font-size: 35px;
  border-radius: 10px;
  transition: 0.6s;
  position: absolute;
  left: 25px;
  top: 16px;
}
.about-main-content .about-information h5 {
  font-size: 25px;
  font-family: "Montserrat", sans-serif;
  margin-bottom: 8px;
}
.about-main-content .about-information span {
  font-weight: 500;
  color: #6c757d;
}
.about-main-content .about-btn {
  margin-top: 30px;
}
.about-main-content .about-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.about-main-content .about-btn .default-btn i {
  background-color: #ffffff;
  color: #6c757d;
}
.about-main-content .about-btn .default-btn:hover {
  background-color: #000000;
}
.about-main-content .about-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

@keyframes ripple {
  0% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.75);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
.about-main-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -1;
  opacity: 70%;
}

/*================================================
Partner Area CSS
=================================================*/
.partner-area {
  background-color: #000000;
}
.partner-area.bg-transparent {
  background-color: transparent;
  position: absolute;
  bottom: 30px;
  right: 0;
  left: 0;
}
.partner-area.bg-transparent .single-partner {
  background-color: #000000;
}
.partner-area.bg-color {
  background-color: #ffffff;
}
.partner-area.bg-color .single-partner {
  background-color: #000000;
}

.single-partner {
  padding: 35px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  text-align: center;
  transition: 0.6s;
  border-radius: 30px 30px 0 30px;
  margin-top: 30px;
  margin-bottom: 30px;
  margin-left: 10px;
  margin-right: 10px;
}
.single-partner:hover {
  transform: unset;
}
.single-partner.style-two {
  border-radius: 0 30px 30px 30px;
}

/*================================================
Offer Area CSS
=================================================*/
.offer-area {
  background-image: url(../../assets/images/offer/offer-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-attachment: fixed;
}
.offer-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #212529;
  z-index: -1;
  opacity: 0.7;
}
.offer-area .container-fluid {
  padding-right: 0;
}

.offer-item {
  padding-top: 45px;
  padding-bottom: 55px;
  max-width: 685px;
  margin-left: auto;
}
.offer-item .content {
  margin-bottom: 30px;
}
.offer-item .content h2 {
  font-size: 45px;
  margin-bottom: 0;
  color: #ffffff;
}
.offer-item .accordion .accordion-item {
  position: relative;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(147, 153, 161, 0.1);
  padding: 15px 15px 15px 90px;
  border-radius: 30px 30px 0 30px;
  margin-bottom: 15px;
}
.offer-item .accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.offer-item .accordion .accordion-item .accordion-title {
  cursor: pointer;
}
.offer-item .accordion .accordion-item .accordion-title i {
  display: inline-block;
  height: 60px;
  width: 60px;
  line-height: 60px;
  background-color: #000000;
  color: #ffffff;
  text-align: center;
  font-size: 30px;
  border-radius: 10px;
  transition: 0.6s;
  position: absolute;
  left: 15px;
  top: 12px;
}
.offer-item .accordion .accordion-item .accordion-title h3 {
  font-size: 22px;
  font-family: "Montserrat", sans-serif;
  margin-bottom: 8px;
  transition: 0.6s;
  cursor: pointer;
}
.offer-item .accordion .accordion-item .accordion-title h3:hover {
  color: #000000;
}
.offer-item .accordion .accordion-item .accordion-title span {
  font-weight: 500;
  color: #6c757d;
  display: inline-block;
  font-size: 15px;
}
.offer-item .accordion .accordion-item .accordion-content {
  display: none;
}
.offer-item .accordion .accordion-item .accordion-content p {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 15px;
}
.offer-item .accordion .accordion-item .accordion-content .offer-btn {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 1px solid #6c757d;
  transition: 0.6s;
}
.offer-item .accordion .accordion-item .accordion-content .offer-btn:hover {
  color: #000000;
  border-bottom: 1px solid #000000;
}
.offer-item .accordion .accordion-item .accordion-content.show {
  display: block;
}
.offer-item .all-offer-btn {
  margin-top: 30px;
}

/*================================================
Reviews Area CSS
=================================================*/
.reviews-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.reviews-image img {
  border-radius: 0 50px 0 0;
}

.reviews-title {
  margin-bottom: 45px;
  margin-left: 30px;
}
.reviews-title h3 {
  font-size: 45px;
  margin-bottom: 0;
}

.reviews-slides {
  position: relative;
  margin-left: 30px;
}
.reviews-slides .reviews-feedback {
  position: relative;
}
.reviews-slides .reviews-feedback .single-feedback {
  position: relative;
  margin-top: 30px;
  margin-bottom: 30px;
}
.reviews-slides .reviews-feedback .single-feedback p {
  position: relative;
  line-height: 1.5;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
  font-size: 20px;
  color: #212529;
}
.reviews-slides .reviews-feedback .single-feedback .icon {
  position: absolute;
  top: -50px;
  left: 0;
  opacity: 50%;
}
.reviews-slides .reviews-feedback .single-feedback .icon i {
  font-size: 120px;
  color: #000000;
  margin-bottom: 20px;
}
.reviews-slides .reviews-thumbnails {
  position: relative;
  z-index: 1;
}
.reviews-slides .reviews-thumbnails .item .img-fill {
  cursor: pointer;
  position: relative;
  text-align: center;
}
.reviews-slides .reviews-thumbnails .item .img-fill img {
  opacity: 0.7;
  transition: 0.6s;
  display: inline-block;
  position: relative;
  border-radius: 0 50px 0 0;
  width: 130px;
  transform: scale(0.9);
}
.reviews-slides .reviews-thumbnails .item .title {
  opacity: 0;
  visibility: hidden;
  transition: 0.6s;
  margin: 0 -262px 30px;
  text-align: left;
}
.reviews-slides .reviews-thumbnails .item .title h3 {
  margin-bottom: 0;
  text-transform: capitalize;
  font-size: 25px;
  font-family: "Montserrat", sans-serif;
}
.reviews-slides .reviews-thumbnails .item .title span {
  display: block;
  color: #000000;
  font-size: 15px;
  margin-top: 8px;
}
.reviews-slides .reviews-thumbnails .item.slick-center .title {
  opacity: 1;
  visibility: visible;
}
.reviews-slides .reviews-thumbnails .item.slick-center .img-fill img {
  opacity: 1;
  transform: scale(1);
}
.reviews-slides .next-arrow, .reviews-slides .prev-arrow {
  position: absolute;
  cursor: pointer;
  bottom: -175px;
  background: #000000;
  border: none;
  color: #212529;
  z-index: 9;
  border-radius: 10px 10px 0 10px;
  outline: 0 !important;
  transition: 0.6s;
  font-size: 25px;
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 52px;
  opacity: 0;
  visibility: hidden;
}
.reviews-slides .next-arrow {
  right: -20px;
}
.reviews-slides .prev-arrow {
  left: -20px;
}
.reviews-slides:hover .next-arrow, .reviews-slides:hover .prev-arrow {
  opacity: 1;
  visibility: visible;
}
.reviews-slides .slick-list {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.slick-slide {
  outline: 0;
}

.reviews-main-shape {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  opacity: 50%;
}
.reviews-main-shape img {
  width: 100%;
}

.clients-item .item {
  margin-left: 30px;
}
.clients-item .item .title {
  margin-bottom: 45px;
}
.clients-item .item .title h3 {
  font-size: 45px;
  margin-bottom: 0;
}
.clients-item .item .single-feedback {
  position: relative;
  margin-top: 30px;
  margin-bottom: 30px;
}
.clients-item .item .single-feedback p {
  position: relative;
  line-height: 1.5;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
  font-size: 20px;
  color: #212529;
}
.clients-item .item .single-feedback .icon {
  position: absolute;
  top: -50px;
  left: 0;
  opacity: 50%;
}
.clients-item .item .single-feedback .icon i {
  font-size: 120px;
  color: #000000;
  margin-bottom: 20px;
}
.clients-item .item .title-info {
  transition: 0.6s;
}
.clients-item .item .title-info h3 {
  margin-bottom: 0;
  text-transform: capitalize;
  font-size: 25px;
}
.clients-item .item .title-info span {
  display: block;
  color: #6b6b84;
  font-size: 14px;
  margin-top: 8px;
  font-weight: 500;
}

.clients-slides.owl-theme .owl-nav {
  margin-top: 0;
}
.clients-slides.owl-theme .owl-nav [class*=owl-] {
  position: absolute;
  right: 60px;
  top: 80%;
  transform: translateY(-80%);
  margin: 0;
  outline: 0;
  width: 45px;
  height: 45px;
  line-height: 52px;
  transition: 0.6s;
  background: #000000;
  color: #ffffff;
  border-radius: 10px 10px 0 10px;
}
.clients-slides.owl-theme .owl-nav [class*=owl-]:hover, .clients-slides.owl-theme .owl-nav [class*=owl-]:focus {
  background-color: #212529;
}
.clients-slides.owl-theme .owl-nav [class*=owl-].owl-next {
  right: 0;
}
.clients-slides.owl-theme .owl-nav [class*=owl-].owl-next i {
  position: relative;
  top: 1px;
  left: 2px;
}
.clients-slides.owl-theme .owl-nav [class*=owl-] i {
  font-size: 25px;
  font-weight: bold;
  position: relative;
  top: 1px;
  right: 2px;
}

/*================================================
Philosophy Area CSS
=================================================*/
.philosophy-area {
  background-image: url(../../assets/images/philosophy/philosophy-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-attachment: fixed;
}
.philosophy-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #000000;
  z-index: -1;
  opacity: 0.95;
}

.philosophy-item .philosophy-content h3 {
  font-size: 38px;
  margin-bottom: 35px;
}
.philosophy-item .philosophy-content h4 {
  font-size: 22px;
  color: #212529;
  font-style: italic;
  margin-bottom: 20px;
}
.philosophy-item .philosophy-content p {
  color: #212529;
}
.philosophy-item .philosophy-content .philosophy-quote {
  background-color: #ffffff;
  padding: 25px;
  position: relative;
  border-radius: 10px;
}
.philosophy-item .philosophy-content .philosophy-quote i {
  font-size: 45px;
  color: #000000;
  position: absolute;
  top: 10px;
  left: 25px;
  opacity: 50%;
}
.philosophy-item .philosophy-content .philosophy-quote p {
  position: relative;
  z-index: 1;
}
.philosophy-item .philosophy-content .philosophy-btn {
  margin-top: 30px;
  text-align: right;
}
.philosophy-item .philosophy-content .philosophy-btn .default-btn {
  background-color: #212529;
  color: #ffffff;
}
.philosophy-item .philosophy-content .philosophy-btn .default-btn i {
  background-color: #000000;
}
.philosophy-item .philosophy-content .philosophy-btn .default-btn:hover {
  background-color: #ffffff;
  color: #212529;
}

.philosophy-slides.owl-theme .owl-dots {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 90%;
  transform: translateY(-90%);
  text-align: left;
}
.philosophy-slides.owl-theme .owl-dots .owl-dot {
  transition: 0.6s;
}
.philosophy-slides.owl-theme .owl-dots .owl-dot span {
  width: 5px;
  height: 35px;
  margin: 0 8px;
  background: #ffffff;
  transition: 0.6s;
  border-radius: 30px;
}
.philosophy-slides.owl-theme .owl-dots .owl-dot:hover span, .philosophy-slides.owl-theme .owl-dots .owl-dot.active span {
  background-color: #212529;
  height: 55px;
}
.philosophy-slides.owl-theme .owl-dots .owl-dot:hover span::before, .philosophy-slides.owl-theme .owl-dots .owl-dot.active span::before {
  opacity: 1;
  visibility: visible;
}
.philosophy-slides.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 0;
  max-width: 1370px;
  margin-left: auto;
}

.philosophy-main-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  opacity: 15%;
}
.philosophy-main-shape img {
  width: 100%;
}

/*================================================
Products Area CSS
=================================================*/
.products-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.products-area .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.products-item {
  margin-bottom: 30px;
  transition: 0.6s;
}
.products-item .products-image {
  border-radius: 25px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  margin-top: 10px;
  margin-bottom: 10px;
  position: relative;
  transition: 0.6s;
}
.products-item .products-image img {
  border-radius: 25px;
}
.products-item .products-image .action-btn {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  transition: 0.6s;
  opacity: 0;
  visibility: hidden;
  margin-top: 10px;
}
.products-item .products-image .action-btn .default-btn {
  background-color: #212529;
  color: #ffffff;
}
.products-item .products-image .action-btn .default-btn i {
  background-color: #000000;
  line-height: 40px;
}
.products-item .products-image .action-btn .default-btn:hover {
  background-color: #000000;
}
.products-item .products-image .action-btn .default-btn:hover i {
  background-color: #ffffff;
}
.products-item .products-content {
  text-align: center;
  margin-top: 25px;
}
.products-item .products-content h3 {
  font-size: 18px;
  margin-bottom: 12px;
  font-style: italic;
}
.products-item .products-content h3 a {
  color: #212529;
}
.products-item .products-content span {
  font-size: 14px;
  color: #6b6b84;
  font-weight: 600;
}
.products-item:hover .products-image .action-btn {
  opacity: 1;
  visibility: visible;
  margin-top: 0;
}

.book-store-btn {
  text-align: center;
  margin-top: 30px;
}
.book-store-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.book-store-btn .default-btn i {
  background-color: #000000;
}
.book-store-btn .default-btn:hover {
  background-color: #000000;
}
.book-store-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.products-slides.owl-theme .owl-nav {
  margin-top: 0;
}
.products-slides.owl-theme .owl-nav [class*=owl-] {
  position: absolute;
  left: -10px;
  top: 30%;
  transform: translateY(-30%);
  margin: 0;
  outline: 0;
  width: 45px;
  height: 45px;
  line-height: 52px;
  transition: 0.6s;
  background: #000000;
  color: #ffffff;
  border-radius: 50%;
  opacity: 0;
  visibility: hidden;
}
.products-slides.owl-theme .owl-nav [class*=owl-]:hover, .products-slides.owl-theme .owl-nav [class*=owl-]:focus {
  background-color: #212529;
}
.products-slides.owl-theme .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: -10px;
}
.products-slides.owl-theme .owl-nav [class*=owl-].owl-next i {
  position: relative;
  top: 1px;
  left: 2px;
}
.products-slides.owl-theme .owl-nav [class*=owl-] i {
  font-size: 25px;
  font-weight: bold;
  position: relative;
  top: 1px;
  right: 2px;
}
.products-slides.owl-theme:hover .owl-nav [class*=owl-] {
  opacity: 1;
  visibility: visible;
  left: 10px;
  transition: 0.6s;
}
.products-slides.owl-theme:hover .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: 10px;
}
.products-slides .owl-item.active.center .products-item {
  transition: 0.6s;
  transform: translateY(30px);
}

.products-main-shape {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  opacity: 50%;
}
.products-main-shape img {
  width: 100%;
}

/*================================================
Blog Area CSS
=================================================*/
.blog-area .container-fluid {
  max-width: 2500px;
  margin: auto;
}

.single-blog {
  margin-bottom: 30px;
  transition: 0.6s;
  position: relative;
}
.single-blog .blog-image {
  position: relative;
  transition: 0.6s;
  text-align: center;
}
.single-blog .blog-image img {
  border-radius: 5px 5px 0 0;
}
.single-blog .blog-image .tag {
  display: inline-block;
  padding: 15px 22px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  background-color: #6c757d;
  color: #000000;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -30px;
  border-radius: 50%;
  transition: 0.6s;
  font-family: "Poppins", sans-serif;
}
.single-blog .blog-image .tag span {
  display: block;
}
.single-blog .blog-content {
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 50px 30px 30px 30px;
  text-align: center;
  border-radius: 0 0 5px 5px;
  transition: 0.6s;
  max-width: 430px;
  margin: auto;
}
.single-blog .blog-content h3 {
  font-size: 22px;
  margin-bottom: 18px;
  line-height: 1.8;
}
.single-blog .blog-content h3 a {
  color: #212529;
}
.single-blog .blog-content .blog-btn .default-btn {
  background-color: #212529;
  color: #ffffff;
}
.single-blog .blog-content .blog-btn .default-btn i {
  background-color: #000000;
  color: #ffffff;
}
.single-blog .blog-content .blog-btn .default-btn:hover i {
  background-color: #ffffff;
  color: #000000;
}
.single-blog:hover {
  transform: translateY(-10px);
}
.single-blog:hover .blog-content {
  background-color: #000000;
  max-width: 100%;
}

.single-blog-item {
  margin-bottom: 30px;
  transition: 0.6s;
  position: relative;
}
.single-blog-item .blog-image {
  position: relative;
  transition: 0.6s;
  text-align: center;
}
.single-blog-item .blog-image img {
  border-radius: 5px 5px 0 0;
  display: inline-block;
}
.single-blog-item .blog-image .tag {
  display: inline-block;
  padding: 16px 25px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  background-color: #6c757d;
  color: #000000;
  position: absolute;
  left: 30px;
  bottom: -45px;
  border-radius: 50%;
  transition: 0.6s;
  font-family: "Poppins", sans-serif;
  z-index: 1;
}
.single-blog-item .blog-image .tag span {
  display: block;
}
.single-blog-item .blog-content {
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 65px 30px 30px 30px;
  border-radius: 0 0 5px 5px;
  transition: 0.6s;
  position: relative;
}
.single-blog-item .blog-content .meta {
  position: absolute;
  top: 20px;
  right: 20px;
}
.single-blog-item .blog-content .meta p {
  margin-bottom: 0;
  color: #212529;
  font-weight: 500;
  font-style: italic;
}
.single-blog-item .blog-content .meta p a {
  color: #212529;
}
.single-blog-item .blog-content h3 {
  font-size: 25px;
  margin-bottom: 22px;
  line-height: 1.5;
}
.single-blog-item .blog-content h3 a {
  color: #212529;
}
.single-blog-item .blog-content .blog-btn .default-btn {
  background-color: #212529;
  color: #ffffff;
}
.single-blog-item .blog-content .blog-btn .default-btn i {
  background-color: #000000;
  color: #ffffff;
}
.single-blog-item .blog-content .blog-btn .default-btn:hover i {
  background-color: #ffffff;
  color: #000000;
}
.single-blog-item:hover {
  transform: translateY(-10px);
}
.single-blog-item:hover .blog-content {
  background-color: #000000;
}

.blog-slides.owl-theme .owl-nav {
  margin-top: 0;
}
.blog-slides.owl-theme .owl-nav [class*=owl-] {
  position: absolute;
  left: -10px;
  top: 30%;
  transform: translateY(-30%);
  margin: 0;
  outline: 0;
  width: 45px;
  height: 45px;
  line-height: 52px;
  transition: 0.6s;
  background: #000000;
  color: #ffffff;
  border-radius: 50%;
  opacity: 0;
  visibility: hidden;
}
.blog-slides.owl-theme .owl-nav [class*=owl-]:hover, .blog-slides.owl-theme .owl-nav [class*=owl-]:focus {
  background-color: #212529;
}
.blog-slides.owl-theme .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: -10px;
}
.blog-slides.owl-theme .owl-nav [class*=owl-].owl-next i {
  position: relative;
  top: 1px;
  left: 2px;
}
.blog-slides.owl-theme .owl-nav [class*=owl-] i {
  font-size: 25px;
  font-weight: bold;
  position: relative;
  top: 1px;
  right: 2px;
}
.blog-slides.owl-theme:hover .owl-nav [class*=owl-] {
  opacity: 1;
  visibility: visible;
  left: -20px;
  transition: 0.6s;
}
.blog-slides.owl-theme:hover .owl-nav [class*=owl-].owl-next {
  left: auto;
  right: -20px;
}

/*================================================
Services Area CSS
=================================================*/
.services-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: #F9F9F9;
}

.tab .tabs_item {
  display: none;
}
.tab .tabs_item:first-child {
  display: block;
}

.services-list-tab .tabs {
  padding-left: 0;
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
  margin-bottom: 45px;
}
.services-list-tab .tabs li {
  flex: 24%;
  max-width: 25%;
  margin-left: 5px;
  margin-right: 5px;
}
.services-list-tab .tabs li a {
  display: inline-block;
  color: #212529;
  background-color: #ffffff;
  box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.09);
  padding: 25px 15px 25px 75px;
  transition: 0.6s;
  width: 100%;
  border-radius: 15px;
  position: relative;
  text-align: center;
}
.services-list-tab .tabs li a i {
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  background-color: #6c757d;
  color: #000000;
  font-size: 25px;
  position: absolute;
  left: 15px;
  top: 12px;
  text-align: center;
  border-radius: 15px;
}
.services-list-tab .tabs li a span {
  display: block;
  font-size: 16px;
  font-weight: 600;
}
.services-list-tab .tabs li.current a {
  color: #ffffff;
  background-color: #6c757d;
}
.services-list-tab .tabs li.current a i {
  background-color: #000000;
  color: #ffffff;
}
.services-list-tab .tab_content .tabs_item .services-tab-image {
  position: relative;
}
.services-list-tab .tab_content .tabs_item .services-tab-image img {
  border-radius: 62% 38% 53% 47%/48% 59% 41% 52%;
}
.services-list-tab .tab_content .tabs_item .services-tab-image .services-tab-shape .shape-1 {
  position: absolute;
  left: 0;
  top: 20px;
  animation: moveleftbounce 5s linear infinite;
}
.services-list-tab .tab_content .tabs_item .services-tab-image .services-tab-shape .shape-2 {
  position: absolute;
  left: -50px;
  top: 30%;
  transform: translateY(-30%);
  max-width: 100px;
  z-index: -1;
  animation: movebounce 5s linear infinite;
}
.services-list-tab .tab_content .tabs_item .services-tab-image .services-tab-shape .shape-3 {
  position: absolute;
  bottom: 70px;
  left: 0;
  animation: moveleftbounce 5s linear infinite;
}
.services-list-tab .tab_content .tabs_item .services-tab-image .services-tab-shape .shape-4 {
  position: absolute;
  right: 60px;
  bottom: 30px;
  animation: movebounce 5s linear infinite;
}
.services-list-tab .tab_content .tabs_item .services-tab-image .circle-shape {
  position: absolute;
  bottom: 50px;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-content-image {
  position: relative;
  margin-bottom: 20px;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-content-image .sub-title {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 35px;
  font-size: 25px;
  font-style: italic;
}
.services-list-tab .tab_content .tabs_item .services-tab-content b {
  font-weight: 400;
  font-size: 15px;
  color: #6b6b84;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-quote {
  background-color: #ffffff;
  padding: 25px;
  position: relative;
  border-radius: 10px;
  margin-top: 20px;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-quote i {
  font-size: 45px;
  color: #000000;
  position: absolute;
  top: 10px;
  left: 25px;
  opacity: 50%;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-quote p {
  position: relative;
  z-index: 1;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-btn {
  margin-top: 25px;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
  padding: 15px 65px 14px 15px;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-btn .default-btn i {
  background-color: #000000;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-btn .default-btn:hover {
  background-color: #000000;
  color: #6c757d;
}
.services-list-tab .tab_content .tabs_item .services-tab-content .services-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.services-main-shape {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.services-main-shape img {
  width: 100%;
}

/*================================================
FAQS Area CSS
=================================================*/
.faqs-area {
  background-color: #F9F9F9;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.faqs-area.bg-color::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: -1;
  height: 30px;
}
.faqs-area.bg-color::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: -1;
  height: 170px;
}
.faqs-area.bg-ffffff {
  margin-top: 100px;
}

.faq-item {
  padding-top: 100px;
  padding-bottom: 65px;
}
.faq-item .content {
  margin-bottom: 45px;
}
.faq-item .content h3 {
  font-size: 45px;
  margin-bottom: 0;
}
.faq-item .content h3 span {
  font-weight: 500;
  font-style: italic;
}
.faq-item .faq-btn {
  margin-top: 30px;
}

.faq-accordion .accordion {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}
.faq-accordion .accordion .accordion-item {
  display: block;
  background: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  margin-bottom: 15px;
  border: none;
}
.faq-accordion .accordion .accordion-item:last-child {
  margin-bottom: 0;
}
.faq-accordion .accordion .accordion-title {
  padding: 25px 40px 25px 20px;
  color: #212529;
  position: relative;
  display: block;
  text-transform: capitalize;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: 0.6s;
}
.faq-accordion .accordion .accordion-title i {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #212529;
  font-size: 15px;
  transition: 0.6s;
}
.faq-accordion .accordion .accordion-title.active {
  color: #000000;
}
.faq-accordion .accordion .accordion-title.active i::before {
  content: "\ed91";
  color: #000000;
}
.faq-accordion .accordion .accordion-content {
  display: none;
  position: relative;
  padding: 18px 20px;
  border-top: 1px solid #000000;
  color: #6b6b84;
}
.faq-accordion .accordion .accordion-content.show {
  display: block;
}

.faq-image {
  background-image: url(../../assets/images/faq.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

.faqs-main-shape {
  position: absolute;
  bottom: 50px;
  z-index: -1;
  left: 0;
  opacity: 50%;
  width: 100%;
}
.faqs-main-shape img {
  width: 100%;
}

/*================================================
Fun Facts Area CSS
=================================================*/
.fun-facts-area {
  background-image: url(../../assets/images/fun-facts-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  margin-top: 100px;
}
.fun-facts-area.bg-top {
  margin-top: 0;
}

.single-fun-fact {
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 30px 35px 30px 120px;
  border-radius: 30px 30px 0 30px;
  position: relative;
  margin-bottom: 30px;
  transition: 0.6s;
}
.single-fun-fact .icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.single-fun-fact .icon i {
  display: inline-block;
  height: 85px;
  width: 85px;
  line-height: 85px;
  background-color: #000000;
  color: #ffffff;
  font-size: 45px;
  text-align: center;
  border-radius: 15px;
}
.single-fun-fact h3 {
  font-size: 25px;
  margin-bottom: 5px;
  line-height: 1;
}
.single-fun-fact h3 .sign-icon {
  font-size: 25px;
  font-weight: 500;
}
.single-fun-fact p {
  margin-bottom: 0;
}

/*================================================
Pricing Area CSS
=================================================*/
.pricing-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.single-pricing-table {
  background-color: #000000;
  padding: 30px 30px 15px;
  margin-bottom: 30px;
  border-radius: 30px 30px 0 30px;
  transition: 0.6s;
}
.single-pricing-table .pricing-header {
  position: relative;
  background-color: #ffffff;
  border-radius: 30px 30px 0 30px;
  padding: 30px 30px 30px 90px;
}
.single-pricing-table .pricing-header i {
  font-size: 50px;
  color: #6c757d;
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
}
.single-pricing-table .pricing-header h3 {
  font-size: 26px;
  margin-bottom: 15px;
}
.single-pricing-table .pricing-header span {
  font-size: 16px;
  color: #6b6b84;
  font-weight: 500;
}
.single-pricing-table .pricing-header .price {
  position: absolute;
  right: 30px;
  top: 30px;
  font-size: 20px;
  font-weight: bold;
}
.single-pricing-table .pricing-features {
  padding-left: 0;
  margin-bottom: 0;
  padding-top: 30px;
}
.single-pricing-table .pricing-features li {
  list-style-type: none;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 18px;
  color: #212529;
  position: relative;
  padding-left: 25px;
  transition: 0.6s;
}
.single-pricing-table .pricing-features li:last-child {
  margin-bottom: 0;
}
.single-pricing-table .pricing-features li i {
  position: absolute;
  left: 0;
  top: 1px;
  color: #ffffff;
  transition: 0.6s;
}
.single-pricing-table .pricing-features li.color-gray {
  color: #eeeeee;
}
.single-pricing-table .pricing-btn {
  position: relative;
  top: 25px;
  text-align: center;
}
.single-pricing-table .pricing-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
  transition: 0.6s;
}
.single-pricing-table .pricing-btn .default-btn i {
  background-color: #000000;
}
.single-pricing-table:hover {
  transform: translateY(-5px);
  background-color: #6c757d;
}
.single-pricing-table:hover .pricing-features li {
  color: #ffffff;
}
.single-pricing-table:hover .pricing-features li i {
  color: #000000;
}
.single-pricing-table:hover .pricing-btn .default-btn {
  background-color: #000000;
  color: #ffffff;
}
.single-pricing-table:hover .pricing-btn .default-btn i {
  background-color: #6c757d;
  color: #ffffff;
}

.pricing-main-shape {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  opacity: 50%;
}
.pricing-main-shape img {
  width: 100%;
}

/*================================================
Download Area CSS
=================================================*/
.download-area {
  background-image: url(../../assets/images/download/download-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
}
.download-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: -1;
  height: 50px;
}
.download-area::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: -1;
  height: 50px;
}

.download-main-content h3 {
  font-size: 45px;
  margin-bottom: 30px;
}
.download-main-content h3 span {
  font-weight: 500;
}
.download-main-content h4 {
  font-size: 25px;
  margin-bottom: 20px;
  font-style: italic;
  font-weight: 500;
}
.download-main-content p {
  margin-bottom: 0;
}
.download-main-content .download-btn {
  margin-top: 25px;
}

/*================================================
Page Banner Area CSS
=================================================*/
.page-banner-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.page-banner-area .container-fluid {
  padding-left: 0;
}

.page-banner-content {
  margin-left: 70px;
}
.page-banner-content h2 {
  font-size: 45px;
  margin-bottom: 20px;
}
.page-banner-content ul {
  padding-left: 0;
  margin-bottom: 0;
}
.page-banner-content ul li {
  display: inline-block;
  list-style-type: none;
  margin-left: 35px;
  font-weight: 600;
  color: #6c757d;
  position: relative;
}
.page-banner-content ul li::before {
  content: "";
  position: absolute;
  left: -26px;
  top: 12px;
  height: 1px;
  width: 15px;
  background: #6c757d;
}
.page-banner-content ul li:first-child {
  margin-left: 0;
}
.page-banner-content ul li:first-child::before {
  display: none;
}
.page-banner-content ul li a {
  display: block;
  color: #6c757d;
}

.page-banner-image {
  position: relative;
}
.page-banner-image .image-shape {
  position: absolute;
  top: -15px;
  right: -50px;
  animation: movebounce 5s linear infinite;
}

.page-banner-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 30%;
  z-index: -1;
}
.page-banner-shape img {
  width: 100%;
}

.page-banner-with-full-image {
  position: relative;
  z-index: 1;
  background-image: url(../../assets/images/page-banner/page-banner-bg-1.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  padding-top: 140px;
  padding-bottom: 150px;
  background-attachment: fixed;
}
.page-banner-with-full-image::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: #000000;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  opacity: 0.6;
}
.page-banner-with-full-image.item-bg1 {
  background-image: url(../../assets/images/page-banner/page-banner-bg-2.jpg);
}
.page-banner-with-full-image.item-bg2 {
  background-image: url(../../assets/images/page-banner/page-banner-bg-3.jpg);
}
.page-banner-with-full-image.item-bg3 {
  background-image: url(../../assets/images/page-banner/page-banner-bg-4.jpg);
}
.page-banner-with-full-image.item-bg4 {
  background-image: url(../../assets/images/page-banner/page-banner-bg-5.jpg);
}

.page-banner-content-two {
  text-align: center;
}
.page-banner-content-two h2 {
  font-size: 45px;
  margin-bottom: 20px;
  color: #ffffff;
}
.page-banner-content-two ul {
  padding-left: 0;
  margin-bottom: 0;
}
.page-banner-content-two ul li {
  display: inline-block;
  list-style-type: none;
  margin-left: 35px;
  font-weight: 600;
  color: #ffffff;
  position: relative;
}
.page-banner-content-two ul li::before {
  content: "";
  position: absolute;
  left: -26px;
  top: 12px;
  height: 1px;
  width: 15px;
  background: #ffffff;
}
.page-banner-content-two ul li:first-child {
  margin-left: 0;
}
.page-banner-content-two ul li:first-child::before {
  display: none;
}
.page-banner-content-two ul li a {
  display: block;
  color: #000000;
}

/*================================================
Coaches Area CSS
=================================================*/
.single-coaches {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}
.single-coaches .image {
  position: relative;
}
.single-coaches .image .social-link {
  position: absolute;
  right: 20px;
  bottom: 35px;
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.single-coaches .image .social-link li {
  display: block;
  margin-bottom: 8px;
}
.single-coaches .image .social-link li:last-child {
  margin-bottom: 0;
}
.single-coaches .image .social-link li a {
  width: 45px;
  height: 45px;
  text-align: center;
  color: #6c757d;
  background-color: #ffffff;
  font-size: 20px;
  position: relative;
  border-radius: 50px;
  transform: scaleY(0);
  transition: 0.6s;
}
.single-coaches .image .social-link li a i {
  position: absolute;
  left: 0;
  top: 50%;
  right: 0;
  transform: translateY(-48%);
}
.single-coaches .image .social-link li a:hover {
  background-color: #000000;
  color: #ffffff;
}
.single-coaches .image .social-link li:nth-child(2) a, .single-coaches .image .social-link li:nth-child(4) a, .single-coaches .image .social-link li:nth-child(6) a, .single-coaches .image .social-link li:nth-child(8) a {
  transform: scaleX(0);
}
.single-coaches .content {
  background-color: #f9f9f9;
  text-align: left;
  padding: 30px;
  position: relative;
}
.single-coaches .content h3 {
  font-size: 28px;
  margin-bottom: 0;
}
.single-coaches .content span {
  margin-top: 15px;
  display: block;
  font-weight: 600;
  color: #6b6b84;
}
.single-coaches .content i {
  display: inline-block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  background-color: #000000;
  transition: 0.6s;
  color: #ffffff;
  border-radius: 50px;
  position: absolute;
  right: 20px;
  top: -25px;
  font-size: 25px;
}
.single-coaches:hover .content i {
  background-color: #6c757d;
  color: #ffffff;
}
.single-coaches:hover .image .social-link li a {
  transform: scale(1) !important;
}

/*================================================
Coaches Details Area CSS
=================================================*/
.coaches-details-image img {
  border-radius: 15px;
}

.coaches-details-content h3 {
  font-size: 35px;
  margin-bottom: 18px;
}
.coaches-details-content b {
  font-weight: 600;
  color: #000000;
  display: inline-block;
  margin-bottom: 16px;
}
.coaches-details-content p {
  margin-bottom: 20px;
}
.coaches-details-content .information {
  padding-left: 0;
  margin-bottom: 0;
}
.coaches-details-content .information li {
  list-style-type: none;
  margin-bottom: 15px;
}
.coaches-details-content .information li:last-child {
  margin-bottom: 0;
}
.coaches-details-content .information li span {
  color: #6b6b84;
  font-weight: 600;
}
.coaches-details-content .information li a {
  color: #6c757d;
  font-weight: 500;
}
.coaches-details-content .information li a:hover {
  color: #000000;
  letter-spacing: 1px;
}
.coaches-details-content .social-link {
  padding-left: 0;
  margin-bottom: 0;
  margin-top: 25px;
}
.coaches-details-content .social-link li {
  list-style-type: none;
  display: inline-block;
  margin-right: 8px;
}
.coaches-details-content .social-link li:last-child {
  margin-right: 0;
}
.coaches-details-content .social-link li a i {
  display: inline-block;
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background-color: #e1e1e1;
  transition: 0.6s;
  color: #6c757d;
  border-radius: 50px;
  transition: 0.6s;
  font-size: 18px;
}
.coaches-details-content .social-link li a i:hover {
  background-color: #000000;
  color: #ffffff;
  transform: translateY(-5px);
}

/*================================================
Events Area CSS
=================================================*/
.psylo-grid-sorting {
  margin-bottom: 30px;
}
.psylo-grid-sorting .result-count p .count {
  font-weight: 700;
  color: #212529;
}
.psylo-grid-sorting .ordering {
  text-align: right;
}
.psylo-grid-sorting .ordering .select-box {
  display: flex;
  align-items: center;
  justify-content: end;
}
.psylo-grid-sorting .ordering label {
  display: inline-block;
  margin-bottom: 0;
  color: #000000;
  margin-right: 5px;
  font-weight: 600;
}
.psylo-grid-sorting .ordering .nice-select {
  display: inline-block;
  width: 215px;
  background: #f8f8f8;
  border-color: #eeeeee;
  color: #212529;
  transition: 0.6s;
  padding: 0 0 0 12px;
  height: 55px;
  line-height: 55px;
  font-weight: bold;
  border-radius: 0;
}
.psylo-grid-sorting .ordering .nice-select .list {
  background-color: #ffffff;
  border-radius: 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  list-style-type: none;
  border: none;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  padding-top: 10px;
  padding-bottom: 10px;
}
.psylo-grid-sorting .ordering .nice-select .list .option {
  line-height: 38px;
  min-height: 38px;
  color: #212529;
  position: relative;
  transition: 0.6s;
  padding-left: 15px;
  padding-right: 25px;
  font-weight: 600;
}
.psylo-grid-sorting .ordering .nice-select .list .option.selected.focus {
  color: #000000;
  background-color: #ffffff;
}
.psylo-grid-sorting .ordering .nice-select .list .option:hover {
  background-color: #000000;
  color: #ffffff;
}
.psylo-grid-sorting .ordering .nice-select:after {
  border-color: #212529;
  height: 8px;
  width: 8px;
  margin-top: -5px;
}
.psylo-grid-sorting .ordering .nice-select:hover {
  border-color: #6c757d;
  background-color: transparent;
}
.psylo-grid-sorting .search-form {
  position: relative;
}
.psylo-grid-sorting .search-form .search-field {
  background-color: #f9f9f9;
  height: 60px;
  padding: 8px 15px;
  border: 1px solid #f9f9f9;
  width: 100%;
  display: block;
  outline: 0;
  transition: 0.6s;
  border-radius: 5px;
  color: #6c757d;
}
.psylo-grid-sorting .search-form .search-field::-moz-placeholder {
  color: #6b6b84;
}
.psylo-grid-sorting .search-form .search-field::placeholder {
  color: #6b6b84;
}
.psylo-grid-sorting .search-form .search-field:focus {
  border-color: #000000;
}
.psylo-grid-sorting .search-form button {
  border: none;
  background-color: #000000;
  color: #ffffff;
  height: 40px;
  width: 40px;
  position: absolute;
  right: 10px;
  padding: 0;
  transition: 0.6s;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  border-radius: 5px;
  cursor: pointer;
}
.psylo-grid-sorting .search-form button i {
  position: absolute;
  left: 0;
  right: 0;
  top: 52%;
  transform: translateY(-52%);
}
.psylo-grid-sorting .search-form button:hover, .psylo-grid-sorting .search-form button:focus {
  background-color: #6c757d;
  color: #ffffff;
}

.single-events-box {
  transition: 0.6s;
  background-color: #ffffff;
  margin-bottom: 30px;
}
.single-events-box .image {
  position: relative;
}
.single-events-box .image .date {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: inline-block;
  background-color: #ffffff;
  color: #6b6b84;
  padding: 8px 20px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
}
.single-events-box .image img {
  border-radius: 5px 5px 0 0;
}
.single-events-box .content {
  padding: 20px;
  position: relative;
  background-color: #f3f3f3;
  border-radius: 0 0 5px 5px;
}
.single-events-box .content h3 {
  margin-bottom: 0;
  line-height: 1.3;
  font-size: 25px;
}
.single-events-box .content h3 a {
  display: inline-block;
}
.single-events-box .content .location {
  display: block;
  color: #6c757d;
  margin-top: 20px;
  position: relative;
  padding-left: 22px;
  font-size: 16px;
  font-weight: bold;
}
.single-events-box .content .location i {
  color: #000000;
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 18px;
}
.single-events-box:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  transform: translateY(-10px);
}

/*================================================
Events Details Area CSS
=================================================*/
.events-details-image {
  position: relative;
  margin-bottom: 50px;
}
.events-details-image #timer {
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
  bottom: 50px;
}
.events-details-image #timer div {
  background-color: #000000;
  color: #ffffff;
  width: 150px;
  height: 150px;
  z-index: 1;
  border-radius: 10px;
  font-size: 60px;
  font-weight: 800;
  margin-left: 8px;
  margin-right: 8px;
}
.events-details-image #timer div span {
  display: block;
  margin-top: -6px;
  font-size: 16px;
  font-weight: 600;
}

.events-details-header {
  margin-bottom: 30px;
}
.events-details-header ul {
  margin-bottom: 0;
  list-style-type: none;
  background-color: #f8f9f8;
  padding: 30px;
}
.events-details-header ul li {
  display: inline-block;
  margin-right: 20px;
  color: #6c757d;
  position: relative;
  padding-left: 25px;
  font-size: 16px;
  font-weight: 600;
}
.events-details-header ul li i {
  color: #000000;
  position: absolute;
  left: 0;
  top: 2px;
  font-size: 18px;
}
.events-details-header ul li:last-child {
  margin-right: 0;
}

.events-details-location {
  margin-bottom: 30px;
}
.events-details-location iframe {
  height: 450px;
  width: 100%;
  border: none;
}

.events-details-desc h3 {
  margin-bottom: 20px;
  font-size: 25px;
}
.events-details-desc h3:not(:first-child) {
  margin-top: 20px;
}

.events-details-info {
  background-color: #f8f9f8;
  border-radius: 5px;
  padding: 35px;
}
.events-details-info .info {
  margin-bottom: 0;
  list-style-type: none;
  padding-left: 0;
}
.events-details-info .info li {
  border-bottom: 1px solid #e5e5e7;
  color: #6c757d;
  font-size: 16px;
  font-weight: 600;
  padding-top: 15px;
  padding-bottom: 15px;
}
.events-details-info .info li span {
  font-weight: bold;
  color: #212529;
  position: relative;
}
.events-details-info .info li.price {
  padding-bottom: 10px;
  color: #000000;
  font-size: 28px;
  font-weight: bold;
}
.events-details-info .info li.price span {
  color: #212529;
  font-size: 16px;
  font-weight: bold;
}
.events-details-info .info li:first-child {
  padding-top: 0;
}
.events-details-info .events-btn-box {
  text-align: center;
  margin-top: 25px;
}
.events-details-info .events-btn-box p {
  margin-top: 20px;
  font-weight: 600;
}
.events-details-info .events-btn-box p a {
  color: #000000;
}
.events-details-info .events-btn-box p a:hover {
  text-decoration: underline;
}
.events-details-info .events-share {
  text-align: center;
  margin-top: 20px;
}
.events-details-info .events-share .share-info {
  display: inline-block;
  position: relative;
  cursor: pointer;
}
.events-details-info .events-share .share-info span {
  display: inline-block;
  color: #000000;
  line-height: 1;
  font-size: 16px;
  font-weight: bold;
}
.events-details-info .events-share .share-info .social-link {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 0;
  margin-top: 12px;
}
.events-details-info .events-share .share-info .social-link li {
  display: inline-block;
  margin-right: 5px;
}
.events-details-info .events-share .share-info .social-link li:last-child {
  margin-right: 0;
}
.events-details-info .events-share .share-info .social-link li a i {
  display: inline-block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  background-color: #e1e1e1;
  font-size: 20px;
  color: #212529;
  position: relative;
  border-radius: 50px;
  transition: 0.6s;
}
.events-details-info .events-share .share-info .social-link li a i:hover {
  background-color: #000000;
  color: #ffffff;
  transform: translateY(-5px);
}

/*================================================
Success Story Area CSS
=================================================*/
.success-story-image {
  position: relative;
  overflow: hidden;
}
.success-story-image img {
  transition: 0.6s;
}
.success-story-image .video-btn {
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 90px;
  background-color: #000000;
  border-radius: 50px;
  color: #ffffff;
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}
.success-story-image .video-btn i {
  font-size: 30px;
  position: relative;
  top: 0;
  left: 4px;
}
.success-story-image .video-btn::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50px;
  border: 1px solid #000000;
  animation: ripple 2s linear 1s infinite;
}
.success-story-image .video-btn::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50px;
  border: 1px solid #000000;
  animation: ripple 2s linear infinite;
}
.success-story-image .video-btn:hover {
  background-color: #ffffff;
  color: #000000;
}
.success-story-image:hover img {
  transform: scale(1.1);
}

.success-story-content h3 {
  font-size: 25px;
  margin-top: 30px;
  margin-bottom: 20px;
}
.success-story-content p {
  margin-bottom: 0;
}

/*================================================
Courses Area CSS
=================================================*/
.courses-area .container-fluid {
  max-width: 2500px;
  margin: auto;
}

.single-courses-box {
  margin-bottom: 30px;
  position: relative;
  border-radius: 5px;
  background-color: #ffffff;
  box-shadow: 0px 8px 16px 0px rgba(146, 184, 255, 0.2);
  transition: 0.6s;
}
.single-courses-box .courses-image {
  border-radius: 5px 5px 0 0;
  position: relative;
  z-index: 1;
}
.single-courses-box .courses-image .image {
  border-radius: 5px 5px 0 0;
}
.single-courses-box .courses-image .image img {
  border-radius: 5px 5px 0 0;
}
.single-courses-box .courses-image .price {
  display: inline-block;
  background-color: #000000;
  color: #ffffff;
  width: 65px;
  height: 65px;
  border-radius: 50%;
  position: absolute;
  right: 20px;
  bottom: -32.5px;
  text-align: center;
  line-height: 65px;
  font-size: 25px;
  font-weight: 600;
}
.single-courses-box .courses-content {
  border-radius: 0 0 5px 5px;
  padding: 30px;
}
.single-courses-box .courses-content .course-author {
  margin-bottom: 20px;
}
.single-courses-box .courses-content .course-author img {
  width: 45px;
  height: 45px;
  margin-right: 10px;
}
.single-courses-box .courses-content .course-author span {
  color: #000000;
  font-size: 16px;
  font-weight: 600;
}
.single-courses-box .courses-content h3 {
  margin-bottom: 15px;
  line-height: 1.5;
  font-size: 22px;
}
.single-courses-box .courses-content h3 a {
  display: inline-block;
}
.single-courses-box .courses-content .courses-box-footer {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
  margin-left: -7px;
  margin-right: -7px;
  margin-top: 20px;
}
.single-courses-box .courses-content .courses-box-footer li {
  color: #6c757d;
  font-size: 15px;
  font-weight: 500;
  position: relative;
  padding-left: 32px;
  padding-right: 8px;
}
.single-courses-box .courses-content .courses-box-footer li i {
  color: #000000;
  position: absolute;
  left: 6px;
  top: 0;
  font-size: 20px;
}
.single-courses-box:hover {
  transform: translateY(-10px);
}

/*================================================
Membership Levels Area CSS
=================================================*/
.membership-levels-table .table {
  --bs-table-bg: transparent;
}
.membership-levels-table .table {
  margin-bottom: 0;
}
.membership-levels-table .table thead th {
  vertical-align: middle;
  background-color: #000000;
  white-space: nowrap;
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  border-left: 1px solid #e98f8f;
  border-right: 1px solid #e98f8f;
  border-bottom: none;
}
.membership-levels-table .table thead th .desc-1 {
  color: #ffffff;
  margin-bottom: 0;
  font-size: 35px;
  font-weight: bold;
  font-family: "Montserrat", sans-serif;
  display: block;
}
.membership-levels-table .table thead th .desc-2 {
  color: #ffffff;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  font-family: "Montserrat", sans-serif;
  display: block;
}
.membership-levels-table .table thead th:first-child {
  border-left: none;
}
.membership-levels-table .table thead th:last-child {
  border-right: none;
}
.membership-levels-table .table tbody tr td {
  padding: 20px 25px;
  white-space: nowrap;
  vertical-align: middle;
  border: none;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
}
.membership-levels-table .table tbody tr td:first-child {
  text-align: left;
}
.membership-levels-table .table tbody tr td.item-check i {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 100%;
  background-color: #0eb582;
  color: #ffffff;
  display: block;
  margin: 0 auto;
}
.membership-levels-table .table tbody tr td.item-none i {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 100%;
  background-color: #d85554;
  color: #ffffff;
  display: block;
  margin: 0 auto;
}
.membership-levels-table .table tbody tr td .select-btn {
  display: inline-block;
  color: #212529;
  background-color: #efefef;
  padding: 15px 35px;
  border-radius: 5px;
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
}
.membership-levels-table .table tbody tr td .select-btn:hover {
  color: #ffffff;
  background-color: #000000;
}

/*================================================
Become Coaches Area CSS
=================================================*/
.become-coaches-image img {
  border-radius: 10px;
}

.become-coaches-form {
  padding: 35px;
  border-radius: 10px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
}
.become-coaches-form .become-coaches-title {
  margin-bottom: 30px;
}
.become-coaches-form .become-coaches-title h3 {
  font-size: 30px;
  margin-bottom: 15px;
}
.become-coaches-form .become-coaches-title span {
  color: #6b6b84;
}
.become-coaches-form form .form-group {
  margin-bottom: 25px;
}
.become-coaches-form form .form-group .form-control {
  height: 60px;
  padding: 20px;
  line-height: initial;
  color: #212529;
  background-color: transparent;
  border: 1px solid #eeeeee;
  border-radius: 15px;
  box-shadow: unset;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 500;
}
.become-coaches-form form .form-group .form-control:focus {
  border: 1px solid #000000;
  background-color: transparent;
}
.become-coaches-form form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.become-coaches-form form .form-group .form-control:focus::placeholder {
  color: transparent;
  transition: 0.6s;
}
.become-coaches-form form textarea.form-control {
  height: 120px !important;
}
.become-coaches-form form .default-btn {
  border: none;
  width: 100%;
  padding: 20px 30px;
}

/*================================================
Courses Details Area CSS
=================================================*/
.courses-details-image {
  position: relative;
  overflow: hidden;
}
.courses-details-image img {
  transition: 0.6s;
}
.courses-details-image .video-btn {
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 90px;
  background-color: #000000;
  border-radius: 50px;
  color: #ffffff;
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}
.courses-details-image .video-btn i {
  font-size: 30px;
  position: relative;
  top: 0;
  left: 4px;
}
.courses-details-image .video-btn::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50px;
  border: 1px solid #000000;
  animation: ripple 2s linear 1s infinite;
}
.courses-details-image .video-btn::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50px;
  border: 1px solid #000000;
  animation: ripple 2s linear infinite;
}
.courses-details-image .video-btn:hover {
  background-color: #ffffff;
  color: #000000;
}
.courses-details-image:hover img {
  transform: scale(1.1);
}

.courses-details-desc {
  margin-top: 50px;
}
.courses-details-desc .nav {
  margin-bottom: 0;
  list-style-type: none;
  background-color: #f8f9f8;
  border: none;
  border-radius: 5px;
}
.courses-details-desc .nav .nav-item {
  margin-bottom: 0;
  margin-right: 40px;
}
.courses-details-desc .nav .nav-item .nav-link {
  border-radius: 0;
  border: none;
  padding: 0;
  transition: 0.6s;
  padding: 30px;
  background-color: #f8f9f8;
  position: relative;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
}
.courses-details-desc .nav .nav-item .nav-link::before {
  content: "";
  bottom: 0;
  height: 2px;
  background-color: #000000;
  position: absolute;
  transition: 0.6s;
  left: 0;
  width: 100%;
  transform: scaleX(0);
}
.courses-details-desc .nav .nav-item .nav-link:hover, .courses-details-desc .nav .nav-item .nav-link.active {
  color: #212529;
}
.courses-details-desc .nav .nav-item .nav-link:hover::before, .courses-details-desc .nav .nav-item .nav-link.active::before {
  transform: scaleX(1);
}
.courses-details-desc .nav .nav-item:last-child {
  margin-right: 0;
}
.courses-details-desc .tab-content {
  margin-top: 40px;
}
.courses-details-desc .tab-content .courses-overview h3 {
  margin-bottom: 20px;
  font-size: 25px;
}
.courses-details-desc .tab-content .courses-overview h3:not(:first-child) {
  margin-top: 20px;
}
.courses-details-desc .tab-content .courses-curriculum {
  border: 1px solid #eeeeee;
  border-radius: 5px;
  padding: 30px;
}
.courses-details-desc .tab-content .courses-curriculum h3 {
  margin-bottom: 18px;
  font-size: 20px;
}
.courses-details-desc .tab-content .courses-curriculum h3:not(:first-child) {
  margin-top: 20px;
}
.courses-details-desc .tab-content .courses-curriculum ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.courses-details-desc .tab-content .courses-curriculum ul li {
  display: block;
}
.courses-details-desc .tab-content .courses-curriculum ul li a {
  background-color: #f8f9f8;
  color: #212529;
  padding-left: 30px;
  padding-right: 30px;
  padding-top: 15px;
  padding-bottom: 15px;
  margin-left: -30px;
  margin-right: -30px;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-name {
  font-size: 15px;
  font-weight: 500;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-meta {
  text-align: right;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-meta .questions {
  display: inline-block;
  background: #e3f1f2;
  color: #2dbbc4;
  text-transform: lowercase;
  border-radius: 3px;
  margin-right: 6px;
  padding: 2px 10px 1.5px;
  font-size: 15px;
  font-weight: 600;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-meta .duration {
  display: inline-block;
  background: #f7e7e8;
  color: #000000;
  text-transform: lowercase;
  border-radius: 3px;
  padding: 2px 10px 1.5px;
  font-size: 15px;
  font-weight: 600;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-meta .status {
  display: inline-block;
  background: #212529;
  color: #ffffff;
  border-radius: 3px;
  margin-left: 6px;
  padding: 2px 10px 1.5px;
  font-size: 15px;
  font-weight: 600;
}
.courses-details-desc .tab-content .courses-curriculum ul li a .courses-meta .status.locked {
  color: #212529;
  background-color: transparent;
  padding: 0;
  margin-left: 8px;
  border-radius: 0;
  position: relative;
  top: 1px;
  font-size: 18px;
  font-weight: normal;
}
.courses-details-desc .tab-content .courses-curriculum ul li a:hover {
  color: #000000;
}
.courses-details-desc .tab-content .courses-curriculum ul li:nth-child(2) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(4) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(6) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(8) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(10) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(12) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(14) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(16) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(18) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(20) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(22) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(24) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(26) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(28) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(30) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(32) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(34) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(36) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(38) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(40) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(42) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(44) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(46) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(48) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(50) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(52) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(54) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(56) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(58) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(60) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(62) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(64) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(66) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(68) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(70) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(72) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(74) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(76) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(78) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(80) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(82) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(84) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(86) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(88) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(90) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(92) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(94) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(96) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(98) a, .courses-details-desc .tab-content .courses-curriculum ul li:nth-child(100) a {
  background-color: #ffffff;
}
.courses-details-desc .tab-content .courses-instructor .instructor-image img {
  border-radius: 15px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content h3 {
  font-size: 25px;
  margin-bottom: 18px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content b {
  font-weight: 600;
  color: #000000;
  display: inline-block;
  margin-bottom: 16px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content p {
  margin-bottom: 20px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content .social-link {
  padding-left: 0;
  margin-bottom: 0;
  margin-top: 25px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content .social-link li {
  list-style-type: none;
  display: inline-block;
  margin-right: 8px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content .social-link li:last-child {
  margin-right: 0;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content .social-link li a i {
  display: inline-block;
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background-color: #e1e1e1;
  transition: 0.6s;
  color: #6c757d;
  border-radius: 50px;
  transition: 0.6s;
  font-size: 18px;
}
.courses-details-desc .tab-content .courses-instructor .instructor-content .social-link li a i:hover {
  background-color: #000000;
  color: #ffffff;
  transform: translateY(-5px);
}
.courses-details-desc .tab-content .courses-reviews h3 {
  margin-bottom: 0;
  display: inline-block;
  margin-right: 15px;
  font-size: 22px;
  font-weight: bold;
  font-family: "Montserrat", sans-serif;
}
.courses-details-desc .tab-content .courses-reviews .rating {
  display: inline-block;
  position: relative;
  top: 1px;
}
.courses-details-desc .tab-content .courses-reviews .rating span {
  font-size: 19px;
  color: #cecfd2;
  margin-right: -2px;
}
.courses-details-desc .tab-content .courses-reviews .rating span.checked {
  color: orange;
}
.courses-details-desc .tab-content .courses-reviews .rating-count {
  margin-top: 10px;
  margin-bottom: 20px;
}
.courses-details-desc .tab-content .courses-reviews .rating-count span {
  display: block;
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
}
.courses-details-desc .tab-content .courses-reviews .row {
  overflow: hidden;
  margin-left: 0;
  margin-right: 0;
}
.courses-details-desc .tab-content .courses-reviews .side {
  float: left;
  width: 9%;
  margin-top: 10px;
}
.courses-details-desc .tab-content .courses-reviews .side div {
  font-size: 16px;
  font-weight: bold;
}
.courses-details-desc .tab-content .courses-reviews .middle {
  margin-top: 14px;
  float: left;
  width: 82%;
}
.courses-details-desc .tab-content .courses-reviews .right {
  text-align: right;
}
.courses-details-desc .tab-content .courses-reviews .bar-container {
  width: 100%;
  background-color: #f1f1f1;
  text-align: center;
  color: #ffffff;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-reviews .bar-5 {
  width: 100%;
  height: 18px;
  background-color: #4CAF50;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-reviews .bar-4 {
  width: 75%;
  height: 18px;
  background-color: #2196F3;
  border-radius: 5px;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-reviews .bar-3 {
  width: 50%;
  height: 18px;
  background-color: #00bcd4;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-reviews .bar-2 {
  width: 25%;
  height: 18px;
  background-color: #ff9800;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-reviews .bar-1 {
  width: 0;
  height: 18px;
  background-color: #f44336;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-review-comments {
  margin-top: 40px;
}
.courses-details-desc .tab-content .courses-review-comments h3 {
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 12px;
  margin-bottom: 10px;
  font-size: 22px;
  font-weight: bold;
  font-family: "Montserrat", sans-serif;
}
.courses-details-desc .tab-content .courses-review-comments .user-review {
  border-bottom: 1px solid #f3f3f3;
  padding: 20px 0 20px 110px;
  position: relative;
}
.courses-details-desc .tab-content .courses-review-comments .user-review img {
  position: absolute;
  left: 0;
  top: 20px;
  width: 90px;
  height: 90px;
  border-radius: 5px;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .sub-comment {
  margin-bottom: 8px;
  font-weight: bold;
}
.courses-details-desc .tab-content .courses-review-comments .user-review p {
  font-size: 14px;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .review-rating {
  display: block;
  margin-bottom: 8px;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .review-rating .review-stars {
  display: inline-block;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .review-rating .review-stars i {
  color: #cecfd2;
  font-size: 18px;
  display: inline-block;
  margin-right: -3px;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .review-rating .review-stars i.checked {
  color: orange;
}
.courses-details-desc .tab-content .courses-review-comments .user-review .review-rating span {
  color: #212529;
  position: relative;
  top: -2px;
  font-weight: 800;
  margin-left: 5px;
}

.courses-details-info {
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 12px 12px 50px 12px;
  margin-top: 50px;
}
.courses-details-info .image {
  text-align: center;
  position: relative;
}
.courses-details-info .image .video-btn {
  display: inline-block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  background-color: #ffffff;
  border-radius: 50%;
  color: #212529;
  position: absolute;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  left: 0;
  margin: auto;
  text-align: center;
  transition: 0.6s;
}
.courses-details-info .image .video-btn i {
  font-size: 35px;
  position: relative;
  top: 8px;
  left: 5px;
  color: #000000;
  transition: 0.6s;
}
.courses-details-info .image .video-btn::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  border: 2px solid #ffffff;
  animation: ripple 1s linear infinite;
}
.courses-details-info .image .video-btn:hover {
  background-color: #000000;
}
.courses-details-info .image .video-btn:hover i {
  color: #ffffff;
}
.courses-details-info .info {
  margin-bottom: 0;
  list-style-type: none;
  padding: 30px 15px;
}
.courses-details-info .info li {
  border-bottom: 1px solid #e5e5e7;
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
  padding-top: 15px;
  padding-bottom: 15px;
}
.courses-details-info .info li span {
  font-weight: 600;
  color: #212529;
}
.courses-details-info .info li.price {
  padding-bottom: 10px;
  color: #000000;
  font-size: 28px;
  font-weight: 800;
}
.courses-details-info .info li.price span {
  color: #212529;
  font-size: 16px;
  font-weight: bold;
}
.courses-details-info .info li:first-child {
  padding-top: 0;
}
.courses-details-info .courses-btn-box {
  text-align: center;
}
.courses-details-info .courses-share {
  text-align: center;
  margin-top: 25px;
}
.courses-details-info .courses-share .share-info {
  display: inline-block;
  position: relative;
  cursor: pointer;
}
.courses-details-info .courses-share .share-info span {
  display: inline-block;
  color: #000000;
  line-height: 1;
  font-size: 16px;
  font-weight: bold;
}
.courses-details-info .courses-share .share-info .social-link {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 0;
  margin-top: 12px;
}
.courses-details-info .courses-share .share-info .social-link li {
  display: inline-block;
  margin-right: 5px;
}
.courses-details-info .courses-share .share-info .social-link li:last-child {
  margin-right: 0;
}
.courses-details-info .courses-share .share-info .social-link li a i {
  display: inline-block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  background-color: #e1e1e1;
  font-size: 20px;
  color: #212529;
  position: relative;
  border-radius: 50px;
  transition: 0.6s;
}
.courses-details-info .courses-share .share-info .social-link li a i:hover {
  background-color: #000000;
  color: #ffffff;
  transform: translateY(-5px);
}

/*================================================
Services Details Area CSS
=================================================*/
.services-details-desc .content-image {
  position: relative;
  margin-bottom: 25px;
}
.services-details-desc .content-image .sub-title {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 35px;
  font-size: 30px;
  font-style: italic;
}
.services-details-desc .overview-image {
  margin-top: 25px;
  margin-bottom: 25px;
}
.services-details-desc .overview-content {
  margin-top: 20px;
  margin-bottom: 25px;
}
.services-details-desc .overview-content h3 {
  font-size: 25px;
  margin-bottom: 20px;
}
.services-details-desc .overview-content .list {
  padding-left: 0;
  margin-bottom: 0;
}
.services-details-desc .overview-content .list li {
  font-size: 15px;
  color: #6b6b84;
  font-weight: 500;
  position: relative;
  margin-bottom: 15px;
  list-style-type: none;
  padding-left: 25px;
}
.services-details-desc .overview-content .list li:last-child {
  margin-bottom: 0;
}
.services-details-desc .overview-content .list li i {
  position: absolute;
  left: 0;
  top: 1px;
  color: #000000;
  font-size: 16px;
}
.services-details-desc .color-text {
  color: #212529;
}
.services-details-desc .overview-quote {
  background-color: #000000;
  padding: 25px;
  position: relative;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.services-details-desc .overview-quote i {
  font-size: 45px;
  color: #ffffff;
  position: absolute;
  top: 10px;
  left: 25px;
  opacity: 50%;
}
.services-details-desc .overview-quote p {
  position: relative;
  z-index: 1;
  color: #ffffff;
}

.services-details-info {
  padding-left: 15px;
}
.services-details-info .services-list {
  padding-left: 0;
  list-style-type: none;
  margin-bottom: 30px;
}
.services-details-info .services-list li {
  margin-bottom: 15px;
}
.services-details-info .services-list li:last-child {
  margin-bottom: 0;
}
.services-details-info .services-list li a {
  display: block;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 20px 30px;
  position: relative;
  font-size: 16px;
  font-weight: bold;
  border-radius: 10px 10px 0 10px;
}
.services-details-info .services-list li a i {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  font-size: 25px;
  border-radius: 10px;
  text-align: center;
  background-color: #000000;
  color: #ffffff;
}
.services-details-info .services-list li a:hover, .services-details-info .services-list li a.active {
  background-color: #6c757d;
  color: #ffffff;
}
.services-details-info .choose-us-services h2 {
  font-size: 25px;
  margin-bottom: 0;
}
.services-details-info .choose-us-services .choose-us-content {
  margin-top: 30px;
  background-color: #000000;
  padding: 30px;
  border-radius: 20px 20px 0 0;
}
.services-details-info .choose-us-services .choose-us-content .icon {
  position: relative;
  padding-left: 85px;
  padding-top: 30px;
  padding-bottom: 30px;
  padding-right: 30px;
  background-color: #ffffff;
  border-radius: 20px 20px 0 0;
}
.services-details-info .choose-us-services .choose-us-content .icon i {
  color: #212529;
  font-size: 50px;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.services-details-info .choose-us-services .choose-us-content h4 {
  font-size: 20px;
  margin-bottom: 12px;
}
.services-details-info .choose-us-services .choose-us-content p {
  margin-bottom: 0;
  font-size: 15px;
}
.services-details-info .appointment-form {
  background-color: #6c757d;
  padding: 30px;
  margin-top: 30px;
}
.services-details-info .appointment-form h3 {
  font-size: 25px;
  margin-bottom: 25px;
  color: #ffffff;
}
.services-details-info .appointment-form form .form-group {
  margin-bottom: 15px;
}
.services-details-info .appointment-form form .form-group .form-control {
  height: 55px;
  padding: 20px;
  line-height: initial;
  color: #6b6b84;
  background-color: #f9f9f9;
  border: 1px solid #f9f9f9;
  border-radius: 10px;
  transition: 0.6s;
  font-weight: 500;
  font-size: 16px;
  width: 100%;
}
.services-details-info .appointment-form form .form-group .form-control::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.services-details-info .appointment-form form .form-group .form-control::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.services-details-info .appointment-form form .form-group .form-control:focus {
  outline: 0;
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: none;
}
.services-details-info .appointment-form form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.services-details-info .appointment-form form .form-group .form-control:focus::placeholder {
  color: transparent;
}
.services-details-info .appointment-form form .default-btn {
  border: none;
}
.services-details-info .appointment-form form .default-btn:hover {
  background-color: #000000;
}

/*================================================
Cart Area CSS
=================================================*/
.cart-table .table {
  --bs-table-bg: transparent;
}
.cart-table table {
  margin-bottom: 0;
}
.cart-table table tbody tr {
  background-color: #F9F9F9 !important;
  border-bottom: 15px solid #ffffff;
  border-top: none;
}
.cart-table table tbody tr:last-child {
  border-bottom: none;
}
.cart-table table tbody tr td {
  vertical-align: middle;
  white-space: nowrap;
  padding: 20px;
  border-left: none;
  border-right: none;
}
.cart-table table tbody tr td.product-thumbnail a {
  display: block;
}
.cart-table table tbody tr td.product-thumbnail a img {
  width: 100px;
  height: 100px;
  border: 1px solid #eeeeee;
}
.cart-table table tbody tr td.product-name a {
  color: #6c757d;
  font-weight: 600;
  display: inline-block;
  text-decoration: none;
  font-family: "Poppins", sans-serif;
  font-style: italic;
  font-size: 20px;
}
.cart-table table tbody tr td.product-name a:hover {
  color: #000000;
}
.cart-table table tbody tr td.product-price .unit-amount {
  font-weight: 600;
  color: #6b6b84;
}
.cart-table table tbody tr td.product-quantity .input-counter {
  max-width: 130px;
  min-width: 130px;
  text-align: center;
  display: inline-block;
  position: relative;
}
.cart-table table tbody tr td.product-quantity .input-counter span {
  position: absolute;
  top: 0;
  background-color: #6c757d;
  cursor: pointer;
  color: #ffffff;
  width: 40px;
  height: 100%;
  line-height: 48px;
  transition: 0.6s;
  border-radius: 15px;
}
.cart-table table tbody tr td.product-quantity .input-counter span.minus-btn {
  left: 0;
}
.cart-table table tbody tr td.product-quantity .input-counter span.plus-btn {
  right: 0;
}
.cart-table table tbody tr td.product-quantity .input-counter input {
  height: 45px;
  color: #6c757d;
  outline: 0;
  display: block;
  border: none;
  text-align: center;
  width: 100%;
  outline: 0;
  box-shadow: none;
  font-weight: 600;
  border-radius: 10px;
}
.cart-table table tbody tr td.product-quantity .input-counter input::-moz-placeholder {
  color: #6c757d;
}
.cart-table table tbody tr td.product-quantity .input-counter input::placeholder {
  color: #6c757d;
}
.cart-table table tbody tr td.product-subtotal .subtotal-amount {
  font-weight: 600;
  color: #6b6b84;
}
.cart-table table tbody tr td.remove a i {
  color: #ffffff;
  position: relative;
  top: -1px;
  font-size: 20px;
  display: block;
  height: 30px;
  width: 30px;
  line-height: 30px;
  background-color: #6c757d;
  text-align: center;
  border-radius: 50px;
}

.cart-buttons {
  margin-top: 30px;
  text-align: right;
}
.cart-buttons .shopping-coupon-code {
  position: relative;
  max-width: 530px;
}
.cart-buttons .shopping-coupon-code .form-control {
  height: 50px;
  color: #212529;
  box-shadow: unset;
  border: 1px solid #f5f5f5;
  background-color: #f5f5f5;
  transition: 0.6s;
  border-radius: 5px;
  padding: 1px 0 0 15px;
  font-size: 15px;
  font-weight: 500;
}
.cart-buttons .shopping-coupon-code .form-control::-moz-placeholder {
  -moz-transition: 0.6s;
  transition: 0.6s;
  color: #6b6b84;
}
.cart-buttons .shopping-coupon-code .form-control::placeholder {
  transition: 0.6s;
  color: #6b6b84;
}
.cart-buttons .shopping-coupon-code .form-control:focus {
  border-color: #000000;
  background-color: transparent;
}
.cart-buttons .shopping-coupon-code .form-control:focus::-moz-placeholder {
  color: transparent;
}
.cart-buttons .shopping-coupon-code .form-control:focus::placeholder {
  color: transparent;
}
.cart-buttons .shopping-coupon-code button {
  position: absolute;
  right: 0;
  top: 0;
  height: 50px;
  background: #6c757d;
  color: #ffffff;
  border: none;
  padding: 0 60px 0 15px;
  line-height: 48px;
  outline: 0;
  transition: 0.6s;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
}
.cart-buttons .shopping-coupon-code button:hover {
  background-color: #000000;
}
.cart-buttons .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.cart-buttons .default-btn i {
  background-color: #000000;
}
.cart-buttons .default-btn:hover {
  background-color: #000000;
}
.cart-buttons .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.cart-totals {
  margin-top: 50px;
}
.cart-totals h3 {
  margin-bottom: 15px;
  font-size: 25px;
}
.cart-totals ul {
  padding-left: 0;
  margin: 0 0 25px;
  list-style-type: none;
}
.cart-totals ul li {
  border-bottom: 1px solid #eeeeee;
  padding: 20px 0;
  color: #212529;
  overflow: hidden;
  font-weight: 600;
}
.cart-totals ul li b {
  font-weight: bold;
}
.cart-totals ul li span {
  float: right;
  color: #6b6b84;
  font-weight: 500;
}
.cart-totals .totals-btn {
  text-align: right;
}
.cart-totals .totals-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.cart-totals .totals-btn .default-btn i {
  background-color: #000000;
}
.cart-totals .totals-btn .default-btn:hover {
  background-color: #000000;
}
.cart-totals .totals-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

/*================================================
Wishlist Area CSS
=================================================*/
.wishlist-table .table {
  --bs-table-bg: transparent;
}
.wishlist-table table {
  margin-bottom: 0;
}
.wishlist-table table tbody tr {
  background-color: #F9F9F9 !important;
  border-bottom: 15px solid #ffffff;
  border-top: none;
}
.wishlist-table table tbody tr:last-child {
  border-bottom: none;
}
.wishlist-table table tbody tr td {
  vertical-align: middle;
  white-space: nowrap;
  padding: 20px;
  border-left: none;
  border-right: none;
}
.wishlist-table table tbody tr td.product-thumbnail a {
  display: block;
}
.wishlist-table table tbody tr td.product-thumbnail a img {
  width: 100px;
  height: 100px;
  border: 1px solid #eeeeee;
}
.wishlist-table table tbody tr td.product-name a {
  color: #6b6b84;
  font-weight: 500;
  display: inline-block;
  text-decoration: none;
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-style: italic;
}
.wishlist-table table tbody tr td.product-name a:hover {
  color: #000000;
}
.wishlist-table table tbody tr td.product-price .unit-amount {
  font-weight: 600;
  font-size: 16px;
  color: #6b6b84;
}
.wishlist-table table tbody tr td.product-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.wishlist-table table tbody tr td.product-btn .default-btn i {
  background-color: #000000;
}
.wishlist-table table tbody tr td.product-btn .default-btn:hover {
  background-color: #000000;
}
.wishlist-table table tbody tr td.product-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}
.wishlist-table table tbody tr td.product-subtotal .remove {
  color: #ffffff;
  float: right;
  position: relative;
  top: -1px;
  font-size: 20px;
  display: block;
  height: 30px;
  width: 30px;
  line-height: 35px;
  background-color: #6c757d;
  text-align: center;
  border-radius: 50px;
}
.wishlist-table .wishlist-btn {
  margin-top: 30px;
  text-align: right;
}
.wishlist-table .wishlist-btn .default-btn {
  background-color: #6c757d;
  color: #ffffff;
}
.wishlist-table .wishlist-btn .default-btn i {
  background-color: #000000;
}
.wishlist-table .wishlist-btn .default-btn:hover {
  background-color: #000000;
}
.wishlist-table .wishlist-btn .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

/*================================================
Checkout Area CSS
=================================================*/
.user-actions {
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  background: #ffffff;
  padding: 15px 20px;
  border-top: 3px solid #000000;
  position: relative;
  margin-bottom: 40px;
}
.user-actions i {
  color: #000000;
  margin-right: 2px;
  font-size: 20px;
  position: relative;
  top: 3px;
}
.user-actions span {
  display: inline-block;
  color: #000000;
  font-weight: bold;
  font-size: 16px;
}
.user-actions span a {
  display: inline-block;
}

.billing-details .title {
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 10px;
  border-bottom: 1px solid #eeeeee;
  font-size: 25px;
}
.billing-details .title::before {
  content: "";
  position: absolute;
  background: #000000;
  bottom: -1px;
  left: 0;
  width: 50px;
  height: 1px;
}
.billing-details .form-group {
  margin-bottom: 25px;
}
.billing-details .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 16px;
}
.billing-details .form-group label .required {
  color: red;
}
.billing-details .form-group .form-control {
  height: 50px;
  color: #212529;
  box-shadow: unset !important;
  border: 1px solid #f5f5f5;
  background-color: #f5f5f5;
  transition: 0.6s;
  border-radius: 3px;
  padding: 1px 0 0 15px;
  font-size: 16px;
  font-weight: 600;
}
.billing-details .form-group .form-control::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.billing-details .form-group .form-control::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.billing-details .form-group .form-control:focus {
  border-color: #000000;
  background-color: transparent;
}
.billing-details .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.billing-details .form-group .form-control:focus::placeholder {
  color: transparent;
}
.billing-details .form-group textarea.form-control {
  height: auto;
  padding-top: 15px;
}
.billing-details .form-group .nice-select {
  display: block;
  width: 215px;
  float: unset;
  width: 100%;
  background: #f8f8f8;
  border-color: #eeeeee;
  color: #212529;
  transition: 0.6s;
  padding: 0 0 0 12px;
  height: 55px;
  line-height: 55px;
  font-size: 16px;
  font-weight: 600;
}
.billing-details .form-group .nice-select .list {
  background-color: #ffffff;
  border-radius: 5px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  list-style-type: none;
  border: none;
  width: 100%;
  margin-bottom: 0;
  margin-top: 0;
  padding-left: 0;
  padding-top: 10px;
  padding-bottom: 10px;
}
.billing-details .form-group .nice-select .list .option {
  line-height: 38px;
  min-height: 38px;
  color: #212529;
  position: relative;
  transition: 0.6s;
  padding-left: 32px;
  padding-right: 25px;
  font-size: 16px;
  font-weight: 600;
}
.billing-details .form-group .nice-select .list .option:hover {
  background-color: #000000 !important;
  color: #ffffff;
}
.billing-details .form-group .nice-select .list .option:hover::before {
  color: #ffffff;
}
.billing-details .form-group .nice-select .list .option.focus, .billing-details .form-group .nice-select .list .option.selected.focus {
  background-color: transparent !important;
  color: #212529;
}
.billing-details .form-group .nice-select .list .option::before {
  content: "\f149";
  position: absolute;
  left: 10px;
  top: 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.6s;
  color: #000000;
  font-family: Flaticon;
  font-size: 12px;
}
.billing-details .form-group .nice-select .list .option.selected::before {
  opacity: 1;
  visibility: visible;
}
.billing-details .form-group .nice-select .list .option.selected:hover {
  background-color: #000000 !important;
  color: #ffffff;
}
.billing-details .form-group .nice-select .list .option.selected:hover::before {
  color: #ffffff;
}
.billing-details .form-group .nice-select:after {
  border-color: #212529;
  height: 8px;
  width: 8px;
  margin-top: -5px;
}
.billing-details .form-group .nice-select:hover {
  border-color: #000000;
  background-color: transparent;
}
.billing-details .form-check {
  margin-bottom: 20px;
}
.billing-details .form-check .form-check-label {
  color: #212529;
  font-weight: 600;
}
.billing-details .form-check label {
  position: relative;
  left: -3px;
  top: 0;
  font-weight: 500;
}
.billing-details .col-lg-12:last-child .form-group {
  margin-bottom: 0;
}

.order-details .title {
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 10px;
  border-bottom: 1px solid #eeeeee;
  font-size: 25px;
}
.order-details .title::before {
  content: "";
  position: absolute;
  background: #000000;
  bottom: -1px;
  left: 0;
  width: 50px;
  height: 1px;
}
.order-details .order-table .table {
  --bs-table-bg: transparent;
}
.order-details .order-table table {
  margin-bottom: 0;
}
.order-details .order-table table thead tr th {
  border-bottom-width: 0;
  vertical-align: middle;
  border-color: #eaedff;
  padding: 12px 20px 10px;
  white-space: nowrap;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: bold;
}
.order-details .order-table table tbody tr td {
  vertical-align: middle;
  color: #6c757d;
  white-space: nowrap;
  border-color: #eaedff;
  font-size: 16px;
  font-weight: 600;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 15px;
  padding-bottom: 15px;
}
.order-details .order-table table tbody tr td.product-name a {
  display: inline-block;
}
.order-details .order-table table tbody tr td.order-subtotal span, .order-details .order-table table tbody tr td.order-shipping span, .order-details .order-table table tbody tr td.total-price span {
  color: #212529;
  font-size: 16px;
  font-weight: 600;
}
.order-details .order-table table tbody tr td.shipping-price, .order-details .order-table table tbody tr td.order-subtotal-price, .order-details .order-table table tbody tr td.product-subtotal {
  color: #212529;
  font-weight: 600;
  font-size: 16px;
}
.order-details .payment-box {
  background-color: #ffffff;
  box-shadow: 0 2px 28px 0 rgba(0, 0, 0, 0.06);
  margin-top: 30px;
  padding: 30px;
}
.order-details .payment-box .payment-method p [type=radio]:checked, .order-details .payment-box .payment-method p [type=radio]:not(:checked) {
  display: none;
}
.order-details .payment-box .payment-method p [type=radio]:checked + label, .order-details .payment-box .payment-method p [type=radio]:not(:checked) + label {
  padding-left: 27px;
  cursor: pointer;
  display: block;
  color: #212529;
  position: relative;
  margin-bottom: 8px;
  font-weight: 600;
}
.order-details .payment-box .payment-method p [type=radio]:checked + label::before, .order-details .payment-box .payment-method p [type=radio]:not(:checked) + label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  width: 18px;
  height: 18px;
  border: 1px solid #dddddd;
  border-radius: 50%;
  background: #ffffff;
}
.order-details .payment-box .payment-method p [type=radio]:checked + label::after, .order-details .payment-box .payment-method p [type=radio]:not(:checked) + label::after {
  content: "";
  width: 12px;
  height: 12px;
  background: #000000;
  position: absolute;
  top: 7px;
  left: 3px;
  border-radius: 50%;
  transition: 0.6s;
}
.order-details .payment-box .payment-method p [type=radio]:not(:checked) + label::after {
  opacity: 0;
  visibility: hidden;
  transform: scale(0);
}
.order-details .payment-box .payment-method p [type=radio]:checked + label::after {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}
.order-details .payment-box .default-btn {
  margin-top: 15px;
  box-shadow: 0px 5px 28.5px 1.5px rgba(149, 152, 200, 0.2);
}

/*================================================
My Account Area CSS
=================================================*/
.login-form {
  padding: 35px;
  border-radius: 5px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  max-width: 650px;
  margin: auto;
}
.login-form h2 {
  margin-bottom: 30px;
  font-size: 30px;
  border-bottom: 1px solid #dedddf;
  padding-bottom: 15px;
  position: relative;
}
.login-form h2::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #000000;
  height: 100%;
  width: 100px;
}
.login-form form .form-group {
  margin-bottom: 25px;
}
.login-form form .form-group label {
  display: block;
  margin-bottom: 5px;
  color: #6b6b84;
  font-weight: 500;
  font-size: 16px;
}
.login-form form .form-group .form-control {
  height: 60px;
  padding: 0 0 0 15px;
  line-height: initial;
  color: #212529;
  background-color: transparent;
  border: 1px solid #eeeeee;
  border-radius: 0;
  box-shadow: unset;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 500;
}
.login-form form .form-group .form-control:focus {
  border: 1px solid #000000;
  background-color: transparent;
}
.login-form form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.login-form form .form-group .form-control:focus::placeholder {
  color: transparent;
  transition: 0.6s;
}
.login-form form .form-check-input {
  margin-top: 2px;
}
.login-form form .lost-your-password {
  text-align: right;
}
.login-form form .lost-your-password a {
  display: inline-block;
  position: relative;
  font-weight: 500;
  font-size: 15px;
  color: #6b6b84;
}
.login-form form .lost-your-password a::before {
  width: 100%;
  height: 1px;
  position: absolute;
  left: 0;
  bottom: 0;
  content: "";
  transition: 0.6s;
  background-color: #eeeeee;
}
.login-form form .lost-your-password a::after {
  width: 0;
  height: 1px;
  position: absolute;
  left: 0;
  transition: 0.6s;
  bottom: 0;
  content: "";
  background-color: #000000;
}
.login-form form .lost-your-password a:hover::before {
  width: 0;
}
.login-form form .lost-your-password a:hover::after {
  width: 100%;
}
.login-form form button {
  border: none;
  margin-top: 25px;
  padding: 15px 30px;
  width: 100%;
  border-radius: 5px;
  cursor: pointer;
  background-color: #000000;
  color: #ffffff;
  transition: 0.6s;
  font-weight: 600;
}
.login-form form button:hover {
  background-color: #6c757d;
}
.login-form.mb-30 {
  margin-bottom: 30px;
}

.register-form {
  padding: 35px;
  border-radius: 5px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  max-width: 650px;
  margin: auto;
}
.register-form h2 {
  margin-bottom: 30px;
  font-size: 30px;
  border-bottom: 1px solid #dedddf;
  padding-bottom: 15px;
  position: relative;
}
.register-form h2::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #000000;
  height: 100%;
  width: 100px;
}
.register-form form .form-group {
  margin-bottom: 25px;
}
.register-form form .form-group label {
  display: block;
  margin-bottom: 5px;
  color: #6b6b84;
  font-weight: 500;
  font-size: 16px;
}
.register-form form .form-group .form-control {
  height: 60px;
  padding: 0 0 0 15px;
  line-height: initial;
  color: #212529;
  background-color: transparent;
  border: 1px solid #eeeeee;
  border-radius: 0;
  box-shadow: unset;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 500;
}
.register-form form .form-group .form-control:focus {
  border: 1px solid #000000;
  background-color: transparent;
}
.register-form form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.register-form form .form-group .form-control:focus::placeholder {
  color: transparent;
  transition: 0.6s;
}
.register-form form .description {
  font-style: italic;
  font-size: 13.5px;
  margin-top: -10px;
  margin-bottom: 15px;
}
.register-form form button {
  border: none;
  padding: 15px 30px;
  width: 100%;
  border-radius: 5px;
  cursor: pointer;
  background-color: #000000;
  color: #ffffff;
  transition: 0.6s;
  font-weight: 600;
}
.register-form form button:hover {
  background-color: #6c757d;
}

/*================================================
Products Details Area CSS
=================================================*/
.products-details-area {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.products-details-slides {
  position: relative;
}
.products-details-slides .products-feedback .item .products-image img {
  border-radius: 0 100px 0 0;
}
.products-details-slides .products-thumbnails {
  position: relative;
  z-index: 1;
  padding-left: 30px;
  padding-top: 50px;
}
.products-details-slides .products-thumbnails .item {
  position: relative;
}
.products-details-slides .products-thumbnails .item .products-image {
  cursor: pointer;
  text-align: center;
}
.products-details-slides .products-thumbnails .item .products-image img {
  padding: 5px;
  border-radius: 0 30px 0 0;
}
.products-details-slides .next-arrow, .products-details-slides .prev-arrow {
  position: absolute;
  cursor: pointer;
  bottom: 45px;
  background: #000000;
  border: none;
  color: #212529;
  z-index: 9;
  border-radius: 10px 10px 0 10px;
  outline: 0 !important;
  transition: 0.6s;
  font-size: 25px;
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 52px;
  opacity: 0;
  visibility: hidden;
}
.products-details-slides .next-arrow {
  right: -35px;
}
.products-details-slides .prev-arrow {
  display: none !important;
}
.products-details-slides:hover .next-arrow, .products-details-slides:hover .prev-arrow {
  opacity: 1;
  visibility: visible;
}
.products-details-slides .slick-list {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.products-details-desc {
  padding-left: 30px;
}
.products-details-desc h3 {
  margin-bottom: 20px;
  font-size: 38px;
}
.products-details-desc .price {
  margin-bottom: 15px;
  color: #6c757d;
  font-size: 25px;
  font-weight: bold;
}
.products-details-desc .products-review {
  margin-bottom: 18px;
  position: relative;
}
.products-details-desc .products-review .rating {
  display: inline-block;
  padding-right: 5px;
  font-size: 18px;
  position: absolute;
  bottom: 15px;
  right: 0;
}
.products-details-desc .products-review .rating i {
  color: #ffba0a;
  display: inline-block;
  margin-right: -2px;
}
.products-details-desc .products-review .rating .rating-count {
  display: inline-block;
  color: #212529;
  line-height: initial;
  position: relative;
  top: -3px;
  font-weight: 500;
}
.products-details-desc .products-review .rating .rating-count:hover {
  color: #000000;
  border-color: #000000;
}
.products-details-desc .products-add-to-cart {
  margin-top: 25px;
}
.products-details-desc .products-add-to-cart .input-counter {
  max-width: 130px;
  min-width: 130px;
  margin-left: 10px;
  text-align: center;
  display: inline-block;
  position: relative;
}
.products-details-desc .products-add-to-cart .input-counter span {
  position: absolute;
  top: 10px;
  background-color: #000000;
  cursor: pointer;
  color: #ffffff;
  width: 30px;
  height: 30px;
  line-height: 35px;
  transition: 0.6s;
  font-size: 25px;
  border-radius: 5px;
}
.products-details-desc .products-add-to-cart .input-counter span.minus-btn {
  left: 10px;
}
.products-details-desc .products-add-to-cart .input-counter span.plus-btn {
  right: 10px;
}
.products-details-desc .products-add-to-cart .input-counter span:hover {
  color: #ffffff;
}
.products-details-desc .products-add-to-cart .input-counter input {
  height: 50px;
  color: #ffffff;
  outline: 0;
  display: block;
  border: none;
  background-color: #6c757d;
  text-align: center;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  border-radius: 10px 10px 0 10px;
}
.products-details-desc .products-add-to-cart .input-counter input::-moz-placeholder {
  color: #212529;
}
.products-details-desc .products-add-to-cart .input-counter input::placeholder {
  color: #212529;
}
.products-details-desc .products-add-to-cart .default-btn {
  top: -2px;
  padding-top: 12.5px;
  padding-bottom: 12.5px;
  border: none;
  background-color: #6c757d;
  color: #ffffff;
}
.products-details-desc .products-add-to-cart .default-btn i {
  background-color: #000000;
}
.products-details-desc .products-add-to-cart .default-btn:hover {
  background-color: #000000;
}
.products-details-desc .products-add-to-cart .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.products-details-tabs {
  margin-top: 50px;
}
.products-details-tabs .nav {
  margin-bottom: 30px;
  list-style-type: none;
  display: block;
}
.products-details-tabs .nav .nav-item {
  display: inline-block;
  margin-right: 10px;
}
.products-details-tabs .nav .nav-item:last-child {
  margin-right: 0;
}
.products-details-tabs .nav .nav-item .nav-link {
  border: none;
  padding: 20px 70px;
  background-color: #E6E6E6;
  position: relative;
  font-size: 16px;
  font-weight: bold;
  color: #212529;
  transition: 0.6s;
  border-radius: 0;
}
.products-details-tabs .nav .nav-item .nav-link:hover, .products-details-tabs .nav .nav-item .nav-link.active {
  background-color: #000000;
  color: #ffffff;
}
.products-details-tabs .nav-tabs {
  border-bottom: none;
}
.products-details-tabs .tab-content .tab-pane .products-reviews {
  position: relative;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content {
  position: relative;
  padding: 30px 30px 30px 170px;
  margin-bottom: 30px;
  background-color: #f9f9f9;
  border-radius: 5px;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content img {
  position: absolute;
  left: 25px;
  top: 28px;
  height: 120px;
  width: 120px;
  border-radius: 5px;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  font-family: "Montserrat", sans-serif;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content span {
  color: #8D8D8D;
  font-size: 14px;
  font-style: italic;
  display: inline-block;
  margin-bottom: 12px;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content p {
  margin-bottom: 0;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content .rating {
  display: inline-block;
  font-size: 25px;
  position: absolute;
  right: 30px;
  top: 30px;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content .rating i {
  color: #ffba0a;
}
.products-details-tabs .tab-content .tab-pane .products-reviews .review-content .rating a {
  font-size: 16px;
  color: #6b6b84;
}
.products-details-tabs .tab-content .tab-pane .products-review-form {
  margin-top: 45px;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form {
  position: relative;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form h3 {
  font-size: 25px;
  margin-bottom: 20px;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group {
  margin-bottom: 15px;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control {
  height: 60px;
  color: #212529;
  box-shadow: unset;
  border: 1px solid #f9f9f9;
  background-color: #f9f9f9;
  transition: 0.6s;
  border-radius: 15px;
  padding: 1px 0 0 15px;
  font-size: 16px;
  font-weight: 600;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control::-moz-placeholder {
  color: #7e7e7e;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control::placeholder {
  color: #7e7e7e;
  transition: 0.6s;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control:focus {
  border-color: #000000;
  background-color: transparent;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group .form-control:focus::placeholder {
  color: transparent;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-group textarea.form-control {
  height: auto;
  padding-top: 15px;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-check {
  margin-bottom: 15px;
  margin-top: 5px;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .form-check label {
  font-size: 16px;
  font-weight: 500;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form form .default-btn {
  color: #ffffff;
  cursor: pointer;
  position: relative;
  z-index: 1;
  border: none;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form .rating {
  display: inline-block;
  font-size: 25px;
  position: absolute;
  right: 0;
  top: 0;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form .rating i {
  color: #ffba0a;
}
.products-details-tabs .tab-content .tab-pane .products-review-form .review-form .rating a {
  font-size: 16px;
  color: #6b6b84;
}

.products-details-shape {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0;
  z-index: -1;
  width: 100%;
  opacity: 50%;
}
.products-details-shape img {
  width: 100%;
}

/*================================================
Blog Details Area CSS
=================================================*/
.blog-details-desc .article-content .title-box {
  text-align: center;
  max-width: 920px;
  margin: auto;
}
.blog-details-desc .article-content .title-box h2 {
  font-size: 35px;
  margin-bottom: 20px;
  line-height: 1.5;
}
.blog-details-desc .article-content .title-box .entry-meta {
  margin-bottom: 30px;
}
.blog-details-desc .article-content .title-box .entry-meta ul {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.blog-details-desc .article-content .title-box .entry-meta ul li {
  position: relative;
  display: inline-block;
  color: #212529;
  margin-right: 20px;
  padding-left: 25px;
}
.blog-details-desc .article-content .title-box .entry-meta ul li a {
  display: inline-block;
  color: #6b6b84;
}
.blog-details-desc .article-content .title-box .entry-meta ul li a:hover {
  color: #000000;
}
.blog-details-desc .article-content .title-box .entry-meta ul li i {
  color: #000000;
  position: absolute;
  left: 0;
  top: 2px;
}
.blog-details-desc .article-content .title-box .entry-meta ul li::before {
  content: "";
  position: absolute;
  top: 12px;
  right: -15px;
  width: 6px;
  height: 1px;
  background: #000000;
}
.blog-details-desc .article-content .title-box .entry-meta ul li:last-child {
  margin-right: 0;
}
.blog-details-desc .article-content .title-box .entry-meta ul li:last-child::before {
  display: none;
}
.blog-details-desc .article-content h3 {
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 25px;
}
.blog-details-desc .article-content blockquote, .blog-details-desc .article-content .blockquote {
  overflow: hidden;
  background-color: #fafafa;
  padding: 30px !important;
  position: relative;
  z-index: 1;
  margin-top: 20px;
  margin-bottom: 20px;
}
.blog-details-desc .article-content blockquote p, .blog-details-desc .article-content .blockquote p {
  color: #212529;
  line-height: 1.5;
  margin-bottom: 0;
  font-style: italic;
  font-weight: 600;
  font-size: 18px !important;
}
.blog-details-desc .article-content blockquote::before, .blog-details-desc .article-content .blockquote::before {
  color: #000000;
  content: "\ee81";
  position: absolute;
  left: 50px;
  top: -50px;
  z-index: -1;
  opacity: 30%;
  font-size: 130px;
  font-family: "Boxicons";
  font-weight: 900;
}
.blog-details-desc .article-content .wp-block-gallery.columns-3 {
  padding-left: 0;
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: 30px;
  margin-bottom: 30px;
}
.blog-details-desc .article-content .wp-block-gallery.columns-3 li {
  flex: 0 0 33.3333%;
  max-width: 33.3333%;
  padding-left: 10px;
  padding-right: 10px;
}
.blog-details-desc .article-content .wp-block-gallery.columns-3 li figure {
  margin-bottom: 0;
}
.blog-details-desc .article-footer {
  display: flex;
  flex-wrap: wrap;
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #eeeeee;
}
.blog-details-desc .article-footer .article-tags {
  flex: 0 0 50%;
  max-width: 50%;
}
.blog-details-desc .article-footer .article-tags span {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}
.blog-details-desc .article-footer .article-tags a {
  display: inline-block;
  background-color: #E6E6E6;
  color: #6b6b84;
  padding: 10px 25px;
  border-radius: 10px;
  margin-right: 5px;
  font-weight: 600;
}
.blog-details-desc .article-footer .article-tags a:last-child {
  margin-right: 0;
}
.blog-details-desc .article-footer .article-tags a:hover {
  background-color: #000000;
  color: #ffffff;
}
.blog-details-desc .article-footer .article-share {
  flex: 0 0 50%;
  max-width: 50%;
  text-align: right;
  margin-top: 10px;
}
.blog-details-desc .article-footer .article-share span {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}
.blog-details-desc .article-footer .article-share a {
  display: inline-block;
  color: #000000;
  width: 32px;
  height: 32px;
  line-height: 35px;
  border-radius: 50%;
  background-color: #eeeef0;
  text-align: center;
  font-size: 14px;
}
.blog-details-desc .article-footer .article-share a:hover {
  color: #ffffff;
  background-color: #000000;
  transform: translateY(-2px);
}

.psylo-post-navigation {
  overflow: hidden;
  margin-top: 35px;
  padding-top: 25px;
  padding-bottom: 25px;
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
}
.psylo-post-navigation .prev-link-wrapper {
  float: left;
}
.psylo-post-navigation .prev-link-wrapper a {
  display: flex;
  align-items: center;
}
.psylo-post-navigation .prev-link-wrapper a:hover .image-prev::after {
  opacity: 1;
  visibility: visible;
}
.psylo-post-navigation .prev-link-wrapper a:hover .image-prev .post-nav-title {
  opacity: 1;
  visibility: visible;
}
.psylo-post-navigation .prev-link-wrapper .image-prev {
  display: inline-block;
  min-width: 100px;
  min-height: 100px;
  border-radius: 5px;
  overflow: hidden;
  vertical-align: top;
  margin-right: 20px;
  position: relative;
  transition: 0.6s;
}
.psylo-post-navigation .prev-link-wrapper .image-prev img {
  border-radius: 50%;
  width: 100px;
  height: 100px;
}
.psylo-post-navigation .prev-link-wrapper .image-prev::after {
  display: block;
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  opacity: 0;
  background-color: #000000;
  visibility: hidden;
  transition: 0.6s;
}
.psylo-post-navigation .prev-link-wrapper .image-prev .post-nav-title {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  margin: 0 auto;
  text-align: center;
  text-transform: uppercase;
  z-index: 2;
  color: #ffffff;
  opacity: 0;
  visibility: hidden;
  transition: 0.6s;
  font-size: 16px;
  font-weight: bold;
}
.psylo-post-navigation .prev-link-wrapper .prev-link-info-wrapper {
  color: #212529;
}
.psylo-post-navigation .prev-link-wrapper .prev-title {
  max-width: 360px;
  display: inline-block;
  font-weight: bold;
  font-size: 18px;
}
.psylo-post-navigation .prev-link-wrapper .default-btn {
  display: inline-block !important;
  margin-top: 25px;
  background-color: #6c757d;
  color: #ffffff;
}
.psylo-post-navigation .prev-link-wrapper .default-btn i {
  background-color: #000000;
}
.psylo-post-navigation .prev-link-wrapper .default-btn:hover {
  background-color: #000000;
}
.psylo-post-navigation .prev-link-wrapper .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}
.psylo-post-navigation .next-link-wrapper {
  float: right;
  text-align: right;
}
.psylo-post-navigation .next-link-wrapper a {
  display: flex;
  align-items: center;
}
.psylo-post-navigation .next-link-wrapper a:hover .image-next::after {
  opacity: 1;
  visibility: visible;
}
.psylo-post-navigation .next-link-wrapper a:hover .image-next .post-nav-title {
  opacity: 1;
  visibility: visible;
}
.psylo-post-navigation .next-link-wrapper .image-next {
  display: inline-block;
  min-width: 100px;
  min-height: 100px;
  border-radius: 5px;
  overflow: hidden;
  vertical-align: top;
  margin-left: 20px;
  position: relative;
  transition: 0.6s;
}
.psylo-post-navigation .next-link-wrapper .image-next img {
  border-radius: 50%;
  width: 100px;
  height: 100px;
}
.psylo-post-navigation .next-link-wrapper .image-next::after {
  display: block;
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  opacity: 0;
  background-color: #000000;
  visibility: hidden;
  transition: 0.6s;
}
.psylo-post-navigation .next-link-wrapper .image-next .post-nav-title {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  margin: 0 auto;
  text-align: center;
  text-transform: uppercase;
  z-index: 2;
  color: #ffffff;
  opacity: 0;
  visibility: hidden;
  transition: 0.6s;
  font-size: 16px;
  font-weight: bold;
}
.psylo-post-navigation .next-link-wrapper .next-link-info-wrapper {
  color: #212529;
}
.psylo-post-navigation .next-link-wrapper .next-title {
  max-width: 360px;
  display: inline-block;
  font-weight: bold;
  font-size: 18px;
}
.psylo-post-navigation .next-link-wrapper .default-btn {
  display: inline-block !important;
  margin-top: 25px;
  background-color: #6c757d;
  color: #ffffff;
}
.psylo-post-navigation .next-link-wrapper .default-btn i {
  background-color: #000000;
}
.psylo-post-navigation .next-link-wrapper .default-btn:hover {
  background-color: #000000;
}
.psylo-post-navigation .next-link-wrapper .default-btn:hover i {
  background-color: #6c757d;
  color: #ffffff;
}

.comments-area {
  margin-top: 30px;
}
.comments-area .comments-title {
  margin-bottom: 30px;
  line-height: initial;
  font-size: 30px;
}
.comments-area ol, .comments-area ul {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.comments-area .comment-list {
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.comments-area .children {
  margin-left: 50px;
}
.comments-area .comment-body {
  padding: 30px 30px 30px 175px;
  color: #212529;
  margin-bottom: 40px;
  position: relative;
  background-color: #f9f9f9;
  border-radius: 5px;
}
.comments-area .comment-body .reply {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 1;
}
.comments-area .comment-body .reply a {
  border: 1px dashed #ded9d9;
  color: #6c757d;
  display: inline-block;
  padding: 8px 20px 8px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 600;
  transition: 0.6s;
}
.comments-area .comment-body .reply a:hover {
  background-color: #000000;
  border: 1px solid #000000;
  color: #ffffff;
}
.comments-area .comment-author {
  font-size: 15px;
  margin-bottom: 0.3em;
  position: relative;
}
.comments-area .comment-author .avatar {
  height: 120px;
  left: -140px;
  position: absolute;
  width: 120px;
  border-radius: 5px;
}
.comments-area .comment-author .fn {
  font-weight: 600;
  font-size: 20px;
}
.comments-area .comment-metadata {
  margin-bottom: 0.8em;
  color: #6b6b84;
  letter-spacing: 0.01em;
  font-size: 14px;
  font-weight: 400;
}
.comments-area .comment-metadata a {
  color: #6b6b84;
}
.comments-area .comment-metadata a:hover {
  color: #000000;
}
.comments-area .comment-respond {
  margin-top: 30px;
}
.comments-area .comment-respond .comment-reply-title {
  margin-bottom: 30px;
  font-size: 30px;
}
.comments-area .comment-respond .comment-form .form-group {
  margin-bottom: 15px;
}
.comments-area .comment-respond .comment-form .form-group .form-control {
  display: block;
  width: 100%;
  height: 70px;
  outline: 0;
  background-color: #f9f9f9;
  border: 1px solid #f9f9f9;
  border-radius: 15px;
  box-shadow: none;
  padding: 10px 25px;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 16px;
  font-weight: 500;
}
.comments-area .comment-respond .comment-form .form-group .form-control::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.comments-area .comment-respond .comment-form .form-group .form-control::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.comments-area .comment-respond .comment-form .form-group .form-control:focus {
  outline: 0;
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: none;
}
.comments-area .comment-respond .comment-form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.comments-area .comment-respond .comment-form .form-group .form-control:focus::placeholder {
  color: transparent;
}
.comments-area .comment-respond .comment-form .form-group textarea.form-control {
  height: 150px;
  padding: 25px;
  line-height: 1.5rem;
}
.comments-area .comment-respond .comment-form .default-btn {
  border: none;
  margin-top: 10px;
}

/*================================================
Purchase Guide Area CSS
=================================================*/
.purchase-guide-content img {
  border-radius: 5px;
  display: block;
  width: 100%;
  margin-bottom: 30px;
}
.purchase-guide-content h3 {
  margin-top: 25px;
  margin-bottom: 15px;
  font-size: 25px;
}
.purchase-guide-content h3 span {
  font-family: "Montserrat", sans-serif;
}
.purchase-guide-content .blockquote, .purchase-guide-content blockquote {
  margin-bottom: 30px;
  background-color: #e6f2f5;
  text-align: left !important;
  padding: 25px !important;
}
.purchase-guide-content .blockquote p, .purchase-guide-content blockquote p {
  margin-bottom: 0;
  line-height: 1.8;
  font-size: 16px !important;
  font-weight: 500;
}
.purchase-guide-content .blockquote::after, .purchase-guide-content blockquote::after {
  display: none;
}
.purchase-guide-content .blockquote::before, .purchase-guide-content blockquote::before {
  display: none;
}
.purchase-guide-content ol, .purchase-guide-content ul {
  margin-top: 20px;
}
.purchase-guide-content ol li, .purchase-guide-content ul li {
  margin-bottom: 10px;
  color: #6c757d;
  line-height: 1.8;
  font-weight: 500;
  font-size: 16px;
}

.sidebar-information {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.sidebar-information li {
  margin-bottom: -1px;
}
.sidebar-information li a {
  position: relative;
  display: block;
  padding: 25px 25px 25px 40px;
  color: #6c757d;
  border: 1px solid #eeeeee;
  font-size: 16px;
  font-weight: bold;
}
.sidebar-information li a::before {
  width: 5px;
  height: 5px;
  transition: 0.6s;
  background-color: #6c757d;
  content: "";
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
}
.sidebar-information li a:hover, .sidebar-information li a.active {
  border-color: #000000;
  background-color: #000000;
  color: #ffffff;
}
.sidebar-information li a:hover::before, .sidebar-information li a.active::before {
  background-color: #ffffff;
}
.sidebar-information li.active a {
  border-color: #000000;
  background-color: #000000;
  color: #ffffff;
}
.sidebar-information li.active a::before {
  background-color: #ffffff;
}

/*================================================
Book Online Area CSS
=================================================*/
.book-online-area {
  background-color: #f9f9f9;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.book-online-form {
  padding: 90px 0 100px;
  border-radius: 10px;
}
.book-online-form.bg-top {
  padding: 30px 0 30px;
}
.book-online-form h3 {
  font-size: 30px;
  margin-bottom: 30px;
}
.book-online-form form .form-group {
  margin-bottom: 15px;
}
.book-online-form form .form-group .form-control {
  height: 60px;
  padding: 20px;
  line-height: initial;
  color: #6b6b84;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  border: 1px solid #ffffff;
  border-radius: 10px;
  transition: 0.6s;
  font-weight: 500;
  font-size: 16px;
  width: 100%;
}
.book-online-form form .form-group .form-control:focus {
  border: 1px solid #000000;
  background-color: transparent;
  box-shadow: unset;
  outline: 0;
}
.book-online-form form .nice-select {
  height: 60px;
  width: 100%;
  line-height: 60px;
  padding-left: 20px;
  padding-right: 20px;
  color: #6b6b84;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  border: 1px solid #ffffff;
  border-radius: 10px;
  transition: 0.6s;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 16px;
}
.book-online-form form .nice-select:focus {
  border: 1px solid #000000;
  background-color: transparent;
  box-shadow: unset;
}
.book-online-form form .nice-select .list {
  background-color: #ffffff;
  box-shadow: 0px 0px 29px 0px rgba(102, 102, 102, 0.1);
  border-radius: 0;
  margin-top: 0;
  margin-bottom: 0;
  width: 100%;
  padding-top: 10px;
  padding-bottom: 10px;
  height: 215px;
  overflow-y: scroll;
}
.book-online-form form .nice-select .list .option {
  transition: 0.6s;
  color: #212529;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 16px;
  font-weight: 500;
}
.book-online-form form .nice-select .list .option:hover {
  background-color: #000000 !important;
  color: #ffffff;
}
.book-online-form form .nice-select .list .option.selected {
  background-color: transparent;
  font-weight: 600;
}
.book-online-form form .nice-select::after {
  height: 8px;
  width: 8px;
  border-color: #000000;
  right: 20px;
}
.book-online-form form .book-online-btn {
  margin-top: 20px;
}
.book-online-form form .book-online-btn .default-btn {
  border: none;
  cursor: pointer;
}

.book-online-image {
  background-image: url(../../assets/images/book-online.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

.book-main-shape {
  position: absolute;
  bottom: 0;
  z-index: -1;
  left: 0;
  opacity: 50%;
  width: 100%;
}
.book-main-shape img {
  width: 100%;
}

/*================================================
Gallery Area CSS
=================================================*/
.single-gallery-item {
  position: relative;
  margin-bottom: 30px;
  z-index: 1;
  border-radius: 5px;
  overflow: hidden;
  cursor: zoom-in;
}
.single-gallery-item a {
  display: block;
  border-radius: 5px;
}
.single-gallery-item a img {
  transition: 0.6s;
}
.single-gallery-item:hover a img {
  transform: scale(1.1);
}

/*================================================
Book Appointment Area CSS
=================================================*/
.book-appointment-form {
  text-align: center;
  max-width: 1050px;
  margin: auto;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 45px 30px;
  border-radius: 15px;
}
.book-appointment-form h3 {
  font-size: 35px;
  margin-bottom: 30px;
}
.book-appointment-form .form-group {
  margin-bottom: 15px;
}
.book-appointment-form .form-group .form-control {
  display: block;
  width: 100%;
  height: 70px;
  outline: 0;
  background-color: #f9f9f9;
  border: 1px solid #f9f9f9;
  border-radius: 15px;
  box-shadow: none;
  padding: 10px 25px;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 16px;
  font-weight: 600;
}
.book-appointment-form .form-group .form-control::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.book-appointment-form .form-group .form-control::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.book-appointment-form .form-group .form-control:focus {
  outline: 0;
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: none;
}
.book-appointment-form .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
.book-appointment-form .form-group .form-control:focus::placeholder {
  color: transparent;
}
.book-appointment-form .form-group textarea.form-control {
  height: 120px;
  padding: 25px;
  line-height: 1.5rem;
}
.book-appointment-form .default-btn {
  border: none;
  margin-top: 10px;
}

/*================================================
404 Error Area CSS
=================================================*/
.error-content {
  text-align: center;
  margin: 0 auto;
  max-width: 750px;
}
.error-content h3 {
  font-size: 38px;
  margin-top: 45px;
  margin-bottom: 20px;
}
.error-content p {
  max-width: 520px;
  margin: 0 auto 25px;
  font-size: 15px;
}

/*================================================
Privacy Policy Area CSS
=================================================*/
.privacy-policy-content img {
  border-radius: 5px;
  display: block;
  width: 100%;
  margin-bottom: 30px;
}
.privacy-policy-content h3 {
  margin-top: 25px;
  margin-bottom: 15px;
  font-size: 25px;
}
.privacy-policy-content h3 span {
  font-family: "Montserrat", sans-serif;
}
.privacy-policy-content .blockquote, .privacy-policy-content blockquote {
  margin-bottom: 30px;
  background-color: #e6f2f5;
  text-align: left !important;
  padding: 25px !important;
}
.privacy-policy-content .blockquote p, .privacy-policy-content blockquote p {
  margin-bottom: 0;
  line-height: 1.8;
  font-size: 16px !important;
  font-weight: 500;
}
.privacy-policy-content .blockquote::after, .privacy-policy-content blockquote::after {
  display: none;
}
.privacy-policy-content .blockquote::before, .privacy-policy-content blockquote::before {
  display: none;
}
.privacy-policy-content ol, .privacy-policy-content ul {
  margin-top: 20px;
}
.privacy-policy-content ol li, .privacy-policy-content ul li {
  margin-bottom: 10px;
  color: #6b6b84;
  line-height: 1.8;
  font-weight: 500;
  font-size: 16px;
}

/*================================================
Terms of Service Area CSS
=================================================*/
.terms-of-service-content img {
  border-radius: 5px;
  display: block;
  width: 100%;
  margin-bottom: 30px;
}
.terms-of-service-content h3 {
  margin-top: 25px;
  margin-bottom: 15px;
  font-size: 25px;
}
.terms-of-service-content h3 span {
  font-family: "Montserrat", sans-serif;
}
.terms-of-service-content .blockquote, .terms-of-service-content blockquote {
  margin-bottom: 30px;
  background-color: #e6f2f5;
  text-align: left !important;
  padding: 25px !important;
}
.terms-of-service-content .blockquote p, .terms-of-service-content blockquote p {
  margin-bottom: 0;
  line-height: 1.8;
  font-size: 16px !important;
  font-weight: 500;
}
.terms-of-service-content .blockquote::after, .terms-of-service-content blockquote::after {
  display: none;
}
.terms-of-service-content .blockquote::before, .terms-of-service-content blockquote::before {
  display: none;
}
.terms-of-service-content ol, .terms-of-service-content ul {
  margin-top: 20px;
}
.terms-of-service-content ol li, .terms-of-service-content ul li {
  margin-bottom: 10px;
  color: #6b6b84;
  line-height: 1.8;
  font-weight: 500;
  font-size: 16px;
}

/*================================================
Coming Soon Area CSS
=================================================*/
.coming-soon-area {
  height: 100vh;
  background-image: url(../../assets/images/coming-soon-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.coming-soon-content {
  max-width: 750px;
  background: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  text-align: center;
  padding: 45px 30px;
  box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
  margin: auto;
}
.coming-soon-content h1 {
  font-size: 45px;
  margin-bottom: 20px;
}
.coming-soon-content h1 a {
  color: #000000;
}
.coming-soon-content h2 {
  font-size: 30px;
  margin-bottom: 0;
}
.coming-soon-content #timer {
  margin-top: 30px;
}
.coming-soon-content #timer div {
  background-color: #000000;
  color: #ffffff;
  width: 130px;
  height: 130px;
  border-radius: 50%;
  font-size: 40px;
  font-weight: bold;
  margin-left: 5px;
  margin-right: 5px;
}
.coming-soon-content #timer div span {
  display: block;
  margin-top: -2px;
  font-size: 15px;
  font-weight: 500;
}
.coming-soon-content .newsletter-form {
  margin-top: 45px;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  padding: 30px;
}
.coming-soon-content .newsletter-form .form-group {
  margin-bottom: 15px;
  width: 100%;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter {
  border: none;
  border: 1px solid #f9f9f9;
  background-color: #f9f9f9;
  padding: 20px;
  color: #212529;
  height: 70px;
  display: block;
  width: 100%;
  transition: 0.6s;
  font-size: 16px;
  font-weight: 500;
  border-radius: 15px;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter:focus {
  box-shadow: none;
  outline: 0;
  background-color: transparent;
  border: 1px solid #000000;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter:focus::-moz-placeholder {
  color: transparent;
}
.coming-soon-content .newsletter-form .form-group .input-newsletter:focus::placeholder {
  color: transparent;
}
.coming-soon-content .newsletter-form .default-btn {
  width: 100%;
  border-radius: 15px;
  border: none;
  position: relative;
  top: 0;
  left: 0;
  padding: 20px;
}
.coming-soon-content .newsletter-form .validation-danger {
  margin-top: 15px;
  color: red;
}

/*================================================
Contact Info Area CSS
=================================================*/
.contact-info-box {
  text-align: center;
  transition: 0.6s;
  box-shadow: 0 2px 48px 0 rgba(0, 0, 0, 0.08);
  background: #000000;
  padding: 35px 35px 30px 35px;
  margin-bottom: 30px;
  position: relative;
  border-radius: 50px 50px 0 50px;
  overflow: hidden;
}
.contact-info-box::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 0;
  background: #6c757d;
  z-index: -1;
  transition: 0.6s;
  border-radius: 50px 50px 0 50px;
}
.contact-info-box .icon {
  margin-bottom: 22px;
  background-color: #ffffff;
  padding: 30px;
  position: relative;
  text-align: left;
  border-radius: 20px 20px 0 0;
}
.contact-info-box .icon i {
  font-size: 35px;
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
}
.contact-info-box .icon h3 {
  margin-bottom: 0;
  transition: 0.6s;
  font-size: 25px;
}
.contact-info-box p {
  margin-bottom: 2px;
  transition: 0.6s;
  color: #6c757d;
}
.contact-info-box p a {
  display: inline-block;
  color: #6c757d;
}
.contact-info-box p a:hover {
  color: #000000;
}
.contact-info-box p i {
  color: #ffffff;
  margin-right: 5px;
}
.contact-info-box p:last-child {
  margin-bottom: 0;
}
.contact-info-box:hover {
  transform: translateY(-10px);
}
.contact-info-box:hover::before {
  height: 100%;
}
.contact-info-box:hover p {
  color: #ffffff;
}
.contact-info-box:hover p a {
  color: #ffffff;
}

/*================================================
Contact Area CSS
=================================================*/
.contact-area {
  background-color: #f9f9f9;
}

.contact-form {
  padding: 90px 0 100px;
}
.contact-form h3 {
  font-size: 45px;
  margin-bottom: 30px;
}
.contact-form h3 span {
  font-weight: 500;
}

#contactForm .form-group {
  margin-bottom: 15px;
}
#contactForm .form-group .form-control {
  height: 60px;
  padding: 20px;
  line-height: initial;
  color: #6b6b84;
  background-color: #ffffff;
  box-shadow: 0 0 1.25rem rgba(108, 118, 134, 0.1);
  border: 1px solid #ffffff;
  border-radius: 10px;
  transition: 0.6s;
  font-weight: 500;
  font-size: 16px;
  width: 100%;
}
#contactForm .form-group .form-control::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
#contactForm .form-group .form-control::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
#contactForm .form-group .form-control:focus {
  outline: 0;
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: none;
}
#contactForm .form-group .form-control:focus::-moz-placeholder {
  color: transparent;
}
#contactForm .form-group .form-control:focus::placeholder {
  color: transparent;
}
#contactForm .form-group textarea.form-control {
  height: auto;
  padding: 20px;
  line-height: 1.5rem;
}
#contactForm .form-group .help-block.with-errors ul {
  color: red;
  margin-bottom: 0;
  margin-top: 10px;
}
#contactForm .form-group .help-block.with-errors ul li {
  font-size: 14px;
  text-align: left;
}
#contactForm .form-check {
  margin-bottom: 20px;
  margin-top: 25px;
}
#contactForm .form-check label {
  font-size: 16px;
  font-weight: 600;
}
#contactForm #msgSubmit {
  margin: 0;
  font-size: 1.3rem;
}
#contactForm #msgSubmit.text-danger, #contactForm #msgSubmit.text-success {
  margin-top: 20px;
  font-size: 20px;
  font-family: "Montserrat", sans-serif;
}
#contactForm .default-btn {
  border: none;
  margin-top: 5px;
}

.contact-image {
  background-image: url(../../assets/images/contact.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

/*================================================
Pagination Area CSS
=================================================*/
.pagination-area {
  margin-top: 20px;
  text-align: center;
}
.pagination-area .page-numbers {
  width: 40px;
  height: 40px;
  background-color: #ffffff;
  box-shadow: 0 0px 30px 5px #e4e4ee;
  color: #212529;
  text-align: center;
  display: inline-block;
  border-radius: 3px;
  line-height: 41px;
  position: relative;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bold;
}
.pagination-area .page-numbers:hover, .pagination-area .page-numbers.current {
  color: #ffffff;
  background-color: #000000;
}
.pagination-area .page-numbers i {
  position: relative;
  top: 1.5px;
}

/*================================================
Subscribe Area CSS
=================================================*/
.subscribe-inner-box {
  background-image: url(../../assets/images/subscribe-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  padding-top: 100px;
  padding-bottom: 100px;
  border-radius: 15px;
  position: relative;
  z-index: 1;
}
.subscribe-inner-box::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  left: 0;
  right: 0;
  top: 0;
  z-index: -1;
  opacity: 0.5;
}
.subscribe-inner-box .subscribe-content {
  text-align: center;
  max-width: 835px;
  margin: auto;
}
.subscribe-inner-box .subscribe-content h2 {
  margin-bottom: 25px;
  font-size: 45px;
  margin-top: -5px;
}
.subscribe-inner-box .subscribe-content p {
  color: #212529;
}
.subscribe-inner-box .subscribe-content .newsletter-form {
  position: relative;
  max-width: 650px;
  margin: 30px auto 0;
}
.subscribe-inner-box .subscribe-content .newsletter-form .input-newsletter {
  display: block;
  width: 100%;
  background-color: #ffffff;
  border: none;
  height: 65px;
  padding: 0 0 0 25px;
  border-radius: 15px;
  padding-top: 0;
  outline: 0;
  color: #212529;
  font-weight: 500;
  transition: 0.6s;
}
.subscribe-inner-box .subscribe-content .newsletter-form .input-newsletter::-moz-placeholder {
  color: #6b6b84;
  -moz-transition: 0.6s;
  transition: 0.6s;
}
.subscribe-inner-box .subscribe-content .newsletter-form .input-newsletter::placeholder {
  color: #6b6b84;
  transition: 0.6s;
}
.subscribe-inner-box .subscribe-content .newsletter-form .input-newsletter:focus::-moz-placeholder {
  color: transparent;
}
.subscribe-inner-box .subscribe-content .newsletter-form .input-newsletter:focus::placeholder {
  color: transparent;
}
.subscribe-inner-box .subscribe-content .newsletter-form button {
  position: relative;
  background: #212529;
  color: #ffffff;
  border: none;
  height: 65px;
  padding: 0 55px 0 25px;
  border-radius: 10px 10px 0 10px;
  transition: 0.6s;
  font-size: 15px;
  font-weight: 700;
  width: 100%;
}
.subscribe-inner-box .subscribe-content .newsletter-form button:hover {
  background-color: #000000;
}
.subscribe-inner-box .subscribe-content .newsletter-form button:hover i {
  background-color: #212529;
}
.subscribe-inner-box .subscribe-content .newsletter-form button i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  display: inline-block;
  height: 38px;
  width: 38px;
  line-height: 38px;
  color: #ffffff;
  border-radius: 10px 10px 0 10px;
  background-color: #000000;
  transition: 0.6s;
}
.subscribe-inner-box .subscribe-content .newsletter-form #validator-newsletter {
  color: red;
  font-weight: 500;
}

.subscribe-area.bg-top {
  margin-bottom: -185px;
  position: relative;
  z-index: 1;
}

/*================================================
Footer Area CSS
=================================================*/
.footer-area {
  background-image: url(../../assets/images/footer-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
}
.footer-area::before {
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #000000;
  opacity: 0.77;
}
.footer-area.bg-top {
  padding-top: 290px;
}

.single-footer-widget {
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}
.single-footer-widget .widget-logo {
  margin-bottom: 30px;
}
.single-footer-widget p {
  color: #ffffff;
  font-size: 15px;
}
.single-footer-widget .widget-share {
  margin-top: 25px;
}
.single-footer-widget .widget-share a {
  margin: 0 5px;
}
.single-footer-widget .widget-share a i {
  display: inline-block;
  height: 35px;
  width: 35px;
  line-height: 35px;
  font-size: 16px;
  background-color: transparent;
  color: #ffffff;
  border: 1px solid #ffffff;
  text-align: center;
  border-radius: 50px;
  transition: 0.6s;
}
.single-footer-widget .widget-share a i:hover {
  background-color: #000000;
  border: 1px solid #000000;
  transform: translateY(-5px);
}
.single-footer-widget h3 {
  font-size: 25px;
  margin-bottom: 30px;
  color: #000000;
  font-weight: 600;
}
.single-footer-widget .quick-links {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.single-footer-widget .quick-links li {
  margin-bottom: 18px;
  color: #ffffff;
  font-size: 15px;
}
.single-footer-widget .quick-links li a {
  display: inline-block;
  color: #ffffff;
  font-weight: 400;
  position: relative;
}
.single-footer-widget .quick-links li a i {
  color: #000000;
  font-size: 14px;
  margin-right: 5px;
}
.single-footer-widget .quick-links li a:hover {
  color: #000000;
}
.single-footer-widget .quick-links li a:hover::before {
  width: 100%;
}
.single-footer-widget .quick-links li a::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  transition: 0.6s;
  background-color: #000000;
}
.single-footer-widget .quick-links li:last-child {
  margin-bottom: 0;
}
.single-footer-widget .footer-contact-info {
  padding-left: 0;
  margin-bottom: 0;
  list-style-type: none;
}
.single-footer-widget .footer-contact-info li {
  margin-bottom: 20px;
  color: #ffffff;
  position: relative;
  padding-left: 40px;
  font-size: 15px;
}
.single-footer-widget .footer-contact-info li i {
  position: absolute;
  left: 0;
  top: 5px;
  font-size: 25px;
  color: #000000;
}
.single-footer-widget .footer-contact-info li span {
  display: block;
  font-weight: 500;
  margin-bottom: 10px;
  color: #000000;
  font-size: 16px;
}
.single-footer-widget .footer-contact-info li a {
  display: inline-block;
  color: #ffffff;
  font-weight: 400;
  position: relative;
}
.single-footer-widget .footer-contact-info li a:hover {
  color: #000000;
}
.single-footer-widget .footer-contact-info li a:hover::before {
  width: 100%;
}
.single-footer-widget .footer-contact-info li a::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  transition: 0.6s;
  background-color: #000000;
}
.single-footer-widget .footer-contact-info li:last-child {
  margin-bottom: 0;
}

/*================================================
Copy Right Area CSS
=================================================*/
.copyright-area {
  padding-top: 30px;
  padding-bottom: 30px;
  background-color: #000000;
}
.copyright-area .copyright-area-content {
  text-align: center;
}
.copyright-area .copyright-area-content p {
  color: #212529;
  font-size: 16px;
  font-weight: 500;
}
.copyright-area .copyright-area-content p a {
  display: inline-block;
  font-weight: bold;
  color: #ffffff;
  transition: 0.6s;
}
.copyright-area .copyright-area-content p a:hover {
  color: #6c757d;
}

/*================================================
Go Top CSS
=================================================*/
.go-top {
  position: fixed;
  cursor: pointer;
  bottom: -100px;
  right: 20px;
  color: #ffffff;
  background-color: #212529;
  z-index: 4;
  width: 35px;
  text-align: center;
  height: 35px;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
  transition: 0.9s;
  overflow: hidden;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);
}
.go-top i {
  position: absolute;
  right: 0;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-size: 20px;
  margin-left: auto;
  margin-right: auto;
}
.go-top.active {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}
.go-top:hover {
  background-color: #000000;
  color: #ffffff;
  transition: 0.6s;
  box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/*=================================
Buy Now Btn
====================================*/
.buy-now-btn {
  right: 20px;
  z-index: 99;
  top: 50%;
  position: fixed;
  transform: translateY(-50%);
  border-radius: 30px;
  display: inline-block;
  color: #ffffff !important;
  background-color: #82b440;
  padding: 3.5px 10px;
  font-size: 12px;
  font-weight: 500;
  animation-name: tada;
  animation-duration: 5s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
}
.buy-now-btn:hover {
  background-color: #000000;
  color: #ffffff !important;
}

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}/*# sourceMappingURL=style.css.map */