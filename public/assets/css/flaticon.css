/*
Flaticon icon font: Flaticon
*/
@font-face {
	font-family: "Flaticon";
	src: url("../../assets/fonts/Flaticon.eot");
	src: url("../../assets/fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
	url("../../assets/fonts/Flaticon.woff2") format("woff2"),
	url("../../assets/fonts/Flaticon.woff") format("woff"),
	url("../../assets/fonts/Flaticon.ttf") format("truetype"),
	url("../../assets/fonts/Flaticon.svg#Flaticon") format("svg");
	font-weight: normal;
	font-style: normal;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
	@font-face {
		font-family: "Flaticon";
		src: url("../../assets/fonts/Flaticon.svg#Flaticon") format("svg");
	}
}
[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
	font-family: Flaticon;
	font-style: normal;
}
.flaticon-mail:before { content: "\f100"; }
.flaticon-phone-call:before { content: "\f101"; }
.flaticon-placeholder:before { content: "\f102"; }
.flaticon-shopping-cart:before { content: "\f103"; }
.flaticon-up-sign:before { content: "\f104"; }
.flaticon-pointer:before { content: "\f105"; }
.flaticon-brain:before { content: "\f106"; }
.flaticon-idea:before { content: "\f107"; }
.flaticon-thinking:before { content: "\f108"; }
.flaticon-autism:before { content: "\f109"; }
.flaticon-bipolar:before { content: "\f10a"; }
.flaticon-confusion:before { content: "\f10b"; }
.flaticon-in-love:before { content: "\f10c"; }
.flaticon-schizophrenia:before { content: "\f10d"; }
.flaticon-analytics:before { content: "\f10e"; }
.flaticon-law:before { content: "\f10f"; }
.flaticon-calm:before { content: "\f110"; }
.flaticon-depression:before { content: "\f111"; }
.flaticon-psychology:before { content: "\f112"; }
.flaticon-empathy:before { content: "\f113"; }
.flaticon-vision:before { content: "\f114"; }
.flaticon-study:before { content: "\f115"; }
.flaticon-dreaming:before { content: "\f116"; }
.flaticon-strength:before { content: "\f117"; }
.flaticon-conflict:before { content: "\f118"; }
.flaticon-confusion-1:before { content: "\f119"; }
.flaticon-inspiration:before { content: "\f11a"; }
.flaticon-observation:before { content: "\f11b"; }
.flaticon-imagination:before { content: "\f11c"; }
.flaticon-anxiety:before { content: "\f11d"; }
.flaticon-open-mind:before { content: "\f11e"; }
.flaticon-mental-health:before { content: "\f11f"; }
.flaticon-target:before { content: "\f120"; }
.flaticon-depression-1:before { content: "\f121"; }
.flaticon-memory:before { content: "\f122"; }
.flaticon-autism-1:before { content: "\f123"; }
.flaticon-opportunities:before { content: "\f124"; }
.flaticon-exhausted:before { content: "\f125"; }
.flaticon-extrovert:before { content: "\f126"; }
.flaticon-mind:before { content: "\f127"; }
.flaticon-anger:before { content: "\f128"; }
.flaticon-learning:before { content: "\f129"; }
.flaticon-dead:before { content: "\f12a"; }
.flaticon-growth:before { content: "\f12b"; }
.flaticon-balance:before { content: "\f12c"; }
.flaticon-bipolar-1:before { content: "\f12d"; }
.flaticon-empathy-1:before { content: "\f12e"; }
.flaticon-introvert:before { content: "\f12f"; }
.flaticon-broken-heart:before { content: "\f130"; }
.flaticon-anxiety-1:before { content: "\f131"; }
.flaticon-greed:before { content: "\f132"; }
.flaticon-education:before { content: "\f133"; }
.flaticon-perfectionist:before { content: "\f134"; }
.flaticon-strategy:before { content: "\f135"; }
.flaticon-peace-of-mind:before { content: "\f136"; }
.flaticon-knowledge:before { content: "\f137"; }
.flaticon-triumph:before { content: "\f138"; }
.flaticon-repairing-service:before { content: "\f139"; }
.flaticon-user:before { content: "\f13a"; }
.flaticon-customer:before { content: "\f13b"; }
.flaticon-trophy:before { content: "\f13c"; }
.flaticon-paper-plane:before { content: "\f13d"; }
.flaticon-plus:before { content: "\f13e"; }
.flaticon-remove:before { content: "\f13f"; }
.flaticon-medal:before { content: "\f140"; }
.flaticon-couple:before { content: "\f141"; }
.flaticon-user-1:before { content: "\f142"; }
.flaticon-user-group:before { content: "\f143"; }
.flaticon-cloud-storage-download:before { content: "\f144"; }
.flaticon-close:before { content: "\f145"; }
.flaticon-spinner-of-dots:before { content: "\f146"; }
.flaticon-calendar:before { content: "\f147"; }
.flaticon-chat-bubble:before { content: "\f148"; }
.flaticon-check:before { content: "\f149"; }
.flaticon-up-arrow:before { content: "\f14a"; }
.flaticon-cancel:before { content: "\f14b"; }
.flaticon-back-arrow:before { content: "\f14c"; }
.flaticon-star:before { content: "\f14d"; }
.flaticon-heart:before { content: "\f14e"; }
.flaticon-youtube:before { content: "\f14f"; }
.flaticon-play-button:before { content: "\f150"; }
.flaticon-play:before { content: "\f151"; }
.flaticon-right-arrow:before { content: "\f152"; }
.flaticon-left-arrow:before { content: "\f153"; }
.flaticon-search:before { content: "\f154"; }