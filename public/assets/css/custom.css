.article-content figure{
    margin: 0;
    padding: 0;
}

.pt-288 {
    padding-top: 288px;
}

@media (max-width: 768px) {
    .pt-288 {
        padding-top: 100px;
    }
}

.login-form form button {
    margin-top: 0;
}

.color-primary{
    color: #000000 !important;
}

.services-details-info .choose-us-services .choose-us-content .icon img {
    color: #212529;
    width: 50px;
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.pt-140 {
    padding-top: 140px;
    padding-bottom: 30px;
}

@media (max-width: 768px) {
    .pt-140 {
        padding-top: 80px;
    }
}

@media only screen and (max-width: 767px) {
    .services-details-desc .content-image .sub-title {
        left: 25px;
        font-size: 18px;
    }
}

.two-line-service-title a {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3; /* Adjust based on your design */
    max-height: calc(2 * 1.3em); /* 2 lines × line-height */
}

.top-header-area.bg-color{
    background-color: black;
}

.top-header-area.bg-color .top-header-information li {
    color: #fff;
}

.top-header-area.bg-color .top-header-information li i {
    color: #fff;
}

.top-header-area.bg-color .top-header-information li a {
    color: #fff;
}

.top-header-area.bg-color .top-header-optional li a i {
    color: #fff;
}

.main-navbar {
    padding: 10px 0;
    background: #fff;
    box-shadow: 0 0 1.25rem rgb(108 123 148 / 10%);
}

/* Logo Sizing */
.navbar-brand img,
.logo img {
    max-width: 120px !important;
    height: auto !important;
}

/* Responsive logo sizing */
@media (max-width: 768px) {
    .navbar-brand img,
    .logo img {
        max-width: 80px !important;
    }
}

.main-navbar .navbar .navbar-nav .nav-item a:hover, .main-navbar .navbar .navbar-nav .nav-item a:focus, .main-navbar .navbar .navbar-nav .nav-item a.active {
    color: #ff6f6f;
}